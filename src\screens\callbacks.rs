use teloxide::types::{CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton};
use teloxide::prelude::Requester;
use teloxide::payloads::SendMessageSetters;
use serde_json::Value;
use std::str::FromStr;
use crate::service::{BotService, TokenInfoService};
use crate::model::{BotError, Blockchain, UserData};
use crate::screens::dashboard::{show_dashboard, show_fresh_dashboard};
use crate::screens::config_screen::{show_config_screen, toggle_config_setting};
use crate::screens::trades_screen::show_trades_screen;
use crate::screens::scan_screen::{self, handle_refresh_token, handle_refresh_token_new_message, handle_sell_token, handle_chart_token, handle_spend_token};
use crate::screens::snipe_config_screen::toggle_snipe_setting;
use crate::service::{DashboardView, DashboardStateService};

pub async fn handle_callback(
    bot_service: &BotService,
    callback_query: CallbackQuery,
) -> Result<(), BotError> {
    let callback_id = callback_query.id.clone();
    let data = callback_query.data.clone().unwrap_or_default();

    let (chat_id, message_id, message_text) = if let Some(message) = callback_query.message.clone() {
        let text = message.text().unwrap_or("").to_string();
        (message.chat.id.0 as i64, message.id.0, text)
    } else {
        bot_service
            .answer_callback_query(&callback_id, Some("Cannot process this action"), false)
            .await?;
        return Ok(());
    };

    let tg_user = match callback_query.from.clone() {
        user => user,
    };

    let user_data = match bot_service.get_or_create_user(chat_id, &tg_user).await {
        Ok(data) => data,
        Err(_) => {
            bot_service
                .answer_callback_query(
                    &callback_id,
                    Some("Failed to retrieve user data. Please restart the bot with /start"),
                    false
                )
                .await?;
            return Ok(());
        }
    };

    let parsed_data: Result<Value, _> = serde_json::from_str(&data);

    if let Ok(json_data) = parsed_data {
        if let Some(command) = json_data.get("command").and_then(|c| c.as_str()) {
            handle_json_command(bot_service, &callback_id, chat_id, message_id, &user_data, command, &data).await?;
            return Ok(());
        }
    }
    handle_string_command(bot_service, &callback_id, chat_id, message_id, &user_data, &data, &message_text).await?;

    Ok(())
}

async fn handle_json_command(
    bot_service: &BotService,
    callback_id: &str,
    chat_id: i64,
    message_id: i32,
    user_data: &UserData,
    command: &str,
    callback_data: &str
) -> Result<(), BotError> {
    println!("Processing JSON command: {}", command);

    match command {
        "view_bsc" => {
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::BSC).await?;
            show_dashboard(bot_service, &updated_user_data, message_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Switched to BSC"), false).await?;
        }
        "view_sol" => {
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::SOL).await?;
            show_dashboard(bot_service, &updated_user_data, message_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Switched to Solana"), false).await?;
        }
        "view_eth" => {
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::ETH).await?;
            show_dashboard(bot_service, &updated_user_data, message_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Switched to Ethereum"), false).await?;
        }
        "view_base" => {
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::BASE).await?;
            show_dashboard(bot_service, &updated_user_data, message_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Switched to Base"), false).await?;
        }
        // 🚀 NEW: Send new message versions (don't edit existing receipts)
        "new_msg_view_bsc" => {
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::BSC).await?;
            // Send new dashboard message instead of editing existing one
            crate::screens::bsc_screen::show_bsc_dashboard(bot_service, &updated_user_data, 0).await?;
            bot_service.answer_callback_query(callback_id, Some("Switched to BSC"), false).await?;
        }
        "new_msg_view_sol" => {
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::SOL).await?;
            // Send new dashboard message instead of editing existing one
            crate::screens::sol_screen::show_sol_dashboard(bot_service, &updated_user_data, 0).await?;
            bot_service.answer_callback_query(callback_id, Some("Switched to Solana"), false).await?;
        }
        "new_msg_view_eth" => {
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::ETH).await?;
            // Send new dashboard message instead of editing existing one
            crate::screens::eth_screen::show_eth_dashboard(bot_service, &updated_user_data, 0).await?;
            bot_service.answer_callback_query(callback_id, Some("Switched to Ethereum"), false).await?;
        }
        "new_msg_view_base" => {
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::BASE).await?;
            // Send new dashboard message instead of editing existing one
            crate::screens::base_screen::show_base_dashboard(bot_service, &updated_user_data, 0).await?;
            bot_service.answer_callback_query(callback_id, Some("Switched to Base"), false).await?;
        }
        "dismiss_message" => {
            bot_service.delete_message(chat_id, message_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Message dismissed"), false).await?;
        }
        "cleanup_chat" => {
            bot_service.cleanup_conversation(chat_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Chat cleaned up"), false).await?;
        }



        data if data.starts_with("{") => {
            match serde_json::from_str::<serde_json::Value>(data) {
                Ok(json_cmd) => {
                    if let Some(command) = json_cmd.get("command").and_then(|c| c.as_str()) {
                        println!("Processing JSON command: {}", command);

                        match command {
                            "set_snipe_amount" | "set_snipe_slippage" | "set_snipe_gas_price" |
                            "set_snipe_gas_limit" | "set_snipe_sell_high" | "set_snipe_sell_low" => {
                                let blockchain_str = json_cmd.get("blockchain").and_then(|b| b.as_str()).unwrap_or("bsc");
                                let token_address = json_cmd.get("address").and_then(|a| a.as_str()).unwrap_or("");

                                let blockchain = match blockchain_str {
                                    "bsc" => Blockchain::BSC,
                                    "sol" => Blockchain::SOL,
                                    "eth" => Blockchain::ETH,
                                    "base" => Blockchain::BASE,
                                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                                };

                                let setting = match command {
                                    "set_snipe_amount" => "amount",
                                    "set_snipe_slippage" => "slippage",
                                    "set_snipe_gas_price" => "gas_price",
                                    "set_snipe_gas_limit" => "gas_limit",
                                    "set_snipe_sell_high" => "sell_high",
                                    "set_snipe_sell_low" => "sell_low",
                                    _ => "unknown",
                                };

                                let current_value = match setting {
                                    "amount" => "0.1".to_string(),
                                    "slippage" => format!("{}%", user_data.get_config(&blockchain).map_or("1.0".to_string(), |c| c.slippage.to_string())),
                                    "gas_price" => format!("{} Gwei", user_data.get_config(&blockchain).map_or("5.0".to_string(), |c| c.max_gas_price.to_string())),
                                    "gas_limit" => format!("{}", user_data.get_config(&blockchain).map_or("250000".to_string(), |c| c.max_gas_limit.to_string())),
                                    "sell_high" => format!("{}%", user_data.get_config(&blockchain).map_or("20.0".to_string(), |c| c.sell_high.to_string())),
                                    "sell_low" => format!("{}%", user_data.get_config(&blockchain).map_or("5.0".to_string(), |c| c.sell_low.to_string())),
                                    _ => "Unknown".to_string(),
                                };

                                let setting_display = match setting {
                                    "amount" => "Snipe Amount",
                                    "slippage" => "Slippage",
                                    "gas_price" => "Max Gas Price",
                                    "gas_limit" => "Gas Limit",
                                    "sell_high" => "Sell High Percentage",
                                    "sell_low" => "Sell Low Percentage",
                                    _ => setting,
                                };

                                let prompt_text = format!("Enter new value for <b>{}</b>\n\nCurrent value: <b>{}</b>\n\nBlockchain: <b>{}</b>\nToken: <code>{}</code>",
                                    setting_display,
                                    current_value,
                                    blockchain_str.to_uppercase(),
                                    token_address
                                );

                                bot_service.answer_callback_query(callback_id, None, false).await?;

                                let prompt_msg = bot_service.send_message(chat_id, &prompt_text).await?;

                                let prompt_msg_id = prompt_msg.id.0;

                                let context = format!("snipe_setting:{}:{}:{}:{}:{}", setting, blockchain_str, token_address, message_id, prompt_msg_id);
                                let mut user_states = scan_screen::get_user_states().write().await;
                                user_states.insert(chat_id, context);
                            },

                            "toggle_snipe_antirug" | "toggle_snipe_antimev" |
                            "toggle_snipe_auto_approve" | "toggle_snipe_auto_sell" => {
                                let blockchain_str = json_cmd.get("blockchain").and_then(|b| b.as_str()).unwrap_or("bsc");
                                let token_address = json_cmd.get("address").and_then(|a| a.as_str()).unwrap_or("");

                                let blockchain = match blockchain_str {
                                    "bsc" => Blockchain::BSC,
                                    "sol" => Blockchain::SOL,
                                    "eth" => Blockchain::ETH,
                                    "base" => Blockchain::BASE,
                                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                                };

                                let setting = match command {
                                    "toggle_snipe_antirug" => "antirug",
                                    "toggle_snipe_antimev" => "antimev",
                                    "toggle_snipe_auto_approve" => "auto_approve",
                                    "toggle_snipe_auto_sell" => "auto_sell",
                                    _ => "unknown",
                                };

                                let mut user_data_mut = user_data.clone();

                                toggle_snipe_setting(bot_service, &mut user_data_mut, message_id, blockchain, token_address, setting).await?;

                                bot_service.answer_callback_query(callback_id, Some("✅ Setting updated"), false).await?;
                            },

                            "show_snipe_config" => {
                                let blockchain_str = json_cmd.get("blockchain").and_then(|b| b.as_str()).unwrap_or("bsc");
                                let token_address = json_cmd.get("address").and_then(|a| a.as_str()).unwrap_or("");

                                let blockchain = match blockchain_str {
                                    "bsc" => Blockchain::BSC,
                                    "sol" => Blockchain::SOL,
                                    "eth" => Blockchain::ETH,
                                    "base" => Blockchain::BASE,
                                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                                };

                                let user_data = bot_service.get_or_create_user(chat_id, &bot_service.bot().get_chat_member(
                                    teloxide::types::ChatId(chat_id),
                                    teloxide::types::UserId(chat_id as u64)
                                ).await?.user).await?;

                                crate::screens::snipe_config_screen::show_snipe_config_screen(
                                    bot_service,
                                    &user_data,
                                    message_id,
                                    blockchain,
                                    token_address
                                ).await?;

                                bot_service.answer_callback_query(callback_id, Some("Showing snipe configuration"), false).await?;
                            },



                            "execute_snipe" => {
                                let blockchain_str = json_cmd.get("blockchain").and_then(|b| b.as_str()).unwrap_or("bsc");
                                let token_address = json_cmd.get("address").and_then(|a| a.as_str()).unwrap_or("");

                                let blockchain = match blockchain_str {
                                    "bsc" => Blockchain::BSC,
                                    "sol" => Blockchain::SOL,
                                    "eth" => Blockchain::ETH,
                                    "base" => Blockchain::BASE,
                                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                                };

                                let executing_text = format!(
                                    "🎯 <b>Executing Snipe</b>\n\n\
                                    Blockchain: <b>{}</b>\n\
                                    Token: <code>{}</code>\n\n\
                                    Please wait while we set up the snipe...",
                                    blockchain.to_string().to_uppercase(),
                                    token_address
                                );

                                bot_service.edit_message(chat_id, message_id, &executing_text).await?;

                                bot_service.answer_callback_query(callback_id, Some("Setting up snipe..."), false).await?;

                                let success_text = format!(
                                    "✅ <b>Snipe Set Up Successfully</b>\n\n\
                                    Blockchain: <b>{}</b>\n\
                                    Token: <code>{}</code>\n\n\
                                    Your snipe has been set up and will execute when liquidity is added.",
                                    blockchain.to_string().to_uppercase(),
                                    token_address
                                );

                                let keyboard = InlineKeyboardMarkup::new(vec![
                                    vec![
                                        InlineKeyboardButton::callback(
                                            "◀️ Back to Dashboard".to_string(),
                                            format!("view_{}", blockchain.as_str()),
                                        ),
                                    ],
                                ]);

                                bot_service.edit_message_with_keyboard(chat_id, message_id, &success_text, keyboard).await?;
                            },

                            _ => {
                                println!("Unhandled JSON command: '{}'", command);
                                bot_service.answer_callback_query(callback_id, Some("Command not implemented"), false).await?;
                            }
                        }
                    } else {
                        return Err(BotError::user_error("Invalid JSON command format".to_string()));
                    }
                },
                Err(e) => {
                    return Err(BotError::user_error(format!("Invalid JSON command: {}", e)));
                }
            }
        },




        cmd if cmd.starts_with("scan_contract_") => {
            let blockchain_str = cmd.trim_start_matches("scan_contract_");
            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            bot_service.answer_callback_query(callback_id, Some(&format!("Scanning {} contract", blockchain_str.to_uppercase())), false).await?;

            let updated_user_data = if user_data.user.current_blockchain != blockchain {
                bot_service.user_service().update_user_blockchain(chat_id, blockchain.clone()).await?
            } else {
                user_data.clone()
            };

            crate::screens::scan_screen::show_scan_prompt(bot_service, &updated_user_data, message_id).await?;
        }
        cmd if cmd.starts_with("show_config_") => {
            let blockchain_str = cmd.trim_start_matches("show_config_");
            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            let user_data_mut = user_data.clone();

            show_config_screen(bot_service, &user_data_mut, message_id, blockchain).await?;

            bot_service.answer_callback_query(callback_id, Some(&format!("Showing {} configuration", blockchain_str.to_uppercase())), false).await?;
        }
        cmd if cmd.starts_with("active_trades_") => {
            let blockchain_str = cmd.trim_start_matches("active_trades_");
            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            show_trades_screen(bot_service, user_data, message_id, blockchain).await?;

            bot_service.answer_callback_query(callback_id, Some(&format!("Showing {} active trades", blockchain_str.to_uppercase())), false).await?;
        }
        cmd if cmd.starts_with("refresh_wallet_") => {
            let blockchain_str = cmd.trim_start_matches("refresh_wallet_");

            match show_dashboard(bot_service, user_data, message_id).await {
                Ok(_) => {},
                Err(e) => {
                    if !e.to_string().contains("MessageNotModified") {
                        return Err(e);
                    }
                }
            }

            bot_service.answer_callback_query(callback_id, Some(&format!("Refreshed {} wallet", blockchain_str)), false).await?;
        }
        cmd if cmd.starts_with("refresh_") => {
            // Handle refresh_{blockchain} commands from unified dashboard
            let blockchain_str = cmd.trim_start_matches("refresh_");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => {
                    bot_service.answer_callback_query(callback_id, Some("Unknown blockchain"), false).await?;
                    return Ok(());
                }
            };

            // Refresh wallet balances
            let updated_user_data = bot_service.user_service().refresh_user_wallets(chat_id).await?;

            // Update the dashboard with refreshed data
            match show_dashboard(bot_service, &updated_user_data, message_id).await {
                Ok(_) => {},
                Err(e) => {
                    if !e.to_string().contains("MessageNotModified") {
                        return Err(e);
                    }
                }
            }

            bot_service.answer_callback_query(callback_id, Some(&format!("Refreshed {} wallet", blockchain_str.to_uppercase())), false).await?;
        }
        cmd if cmd.starts_with("scan_") => {
            // Handle scan_{blockchain} commands from unified dashboard
            let blockchain_str = cmd.trim_start_matches("scan_");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => {
                    bot_service.answer_callback_query(callback_id, Some("Unknown blockchain"), false).await?;
                    return Ok(());
                }
            };

            // Answer the callback query FIRST to prevent timeout
            bot_service.answer_callback_query(callback_id, Some(&format!("Opening {} contract scanner", blockchain_str.to_uppercase())), false).await?;

            // Update user's current blockchain if different
            let updated_user_data = if user_data.current_blockchain() != blockchain {
                bot_service.user_service().update_user_blockchain(chat_id, blockchain).await?
            } else {
                user_data.clone()
            };

            // Show scan prompt with the specific blockchain
            scan_screen::show_scan_prompt_with_blockchain(bot_service, &updated_user_data, message_id, &blockchain).await?;
        }
        cmd if cmd.starts_with("generate_new_") && cmd.ends_with("_wallet") => {
            let blockchain_str = cmd.trim_start_matches("generate_new_").trim_end_matches("_wallet");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            let confirm_text = format!(
                "⚠️ <b>WARNING: Generate New {} Wallet</b>\n\n\
                You are about to generate a new wallet for {} blockchain.\n\n\
                <b>YOUR CURRENT WALLET AND ALL FUNDS WILL NO LONGER BE ACCESSIBLE THROUGH THIS BOT!</b>\n\n\
                This action cannot be undone. Are you absolutely sure you want to continue?",
                blockchain_str.to_uppercase(),
                blockchain_str.to_uppercase()
            );

            let keyboard = InlineKeyboardMarkup::new(vec![
                vec![
                    InlineKeyboardButton::callback(
                        "✅ Yes, Generate New Wallet".to_string(),
                        format!("confirm_new_wallet:{}", blockchain.as_str()),
                    ),
                ],
                vec![
                    InlineKeyboardButton::callback(
                        "❌ No, Keep Current Wallet".to_string(),
                        format!("view_{}", blockchain.as_str()),
                    ),
                ],
            ]);

            bot_service.edit_message_with_keyboard(chat_id, message_id, &confirm_text, keyboard).await?;

            bot_service.answer_callback_query(callback_id, None, false).await?;
        }
        cmd if cmd.starts_with("toggle_") => {
            let parts: Vec<&str> = cmd.split('_').collect();
            if parts.len() >= 3 {
                let (setting, blockchain_str) = if parts.len() >= 4 && (parts[1] == "auto") {
                    (format!("{}_{}", parts[1], parts[2]), parts[3])
                } else {
                    (parts[1].to_string(), parts[2])
                };

                let blockchain = match blockchain_str {
                    "bsc" => Blockchain::BSC,
                    "sol" => Blockchain::SOL,
                    "eth" => Blockchain::ETH,
                    "base" => Blockchain::BASE,
                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                };

                let mut user_data_mut = user_data.clone();

                toggle_config_setting(bot_service, &mut user_data_mut, message_id, blockchain, &setting).await?;

                bot_service.answer_callback_query(callback_id, Some("✅ Setting updated"), false).await?;
            } else {
                bot_service.answer_callback_query(callback_id, Some("Invalid setting format"), false).await?;
            }
        }
        cmd if cmd.starts_with("set_") => {
            let parts: Vec<&str> = cmd.split('_').collect();
            if parts.len() >= 3 {
                let setting_parts: Vec<&str> = parts[1..parts.len()-1].to_vec();
                let setting = setting_parts.join("_");
                let blockchain_str = parts[parts.len()-1];

                let blockchain = match blockchain_str {
                    "bsc" => Blockchain::BSC,
                    "sol" => Blockchain::SOL,
                    "eth" => Blockchain::ETH,
                    "base" => Blockchain::BASE,
                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                };

                let current_value = match setting.as_str() {
                    "slippage" => format!("{}%", user_data.get_config(&blockchain).map_or("1.0".to_string(), |c| c.slippage.to_string())),
                    "gas_price" => format!("{} Gwei", user_data.get_config(&blockchain).map_or("5.0".to_string(), |c| c.max_gas_price.to_string())),
                    "gas_limit" => format!("{}", user_data.get_config(&blockchain).map_or("250000".to_string(), |c| c.max_gas_limit.to_string())),
                    "min_liquidity" => format!("${}", user_data.get_config(&blockchain).map_or("1.0".to_string(), |c| c.min_liquidity.to_string())),
                    "max_market_cap" => format!("${}", user_data.get_config(&blockchain).map_or("1000000".to_string(), |c| c.max_market_cap.to_string())),
                    "max_liquidity" => format!("${}", user_data.get_config(&blockchain).map_or("10.0".to_string(), |c| c.max_liquidity.to_string())),
                    "sell_high" => format!("{}%", user_data.get_config(&blockchain).map_or("20.0".to_string(), |c| c.sell_high.to_string())),
                    "sell_low" => format!("{}%", user_data.get_config(&blockchain).map_or("5.0".to_string(), |c| c.sell_low.to_string())),
                    _ => "Unknown".to_string(),
                };

                let setting_display = match setting.as_str() {
                    "slippage" => "Slippage",
                    "gas_price" => "Max Gas Price",
                    "gas_limit" => "Gas Limit",
                    "min_liquidity" => "Minimum Liquidity",
                    "max_market_cap" => "Maximum Market Cap",
                    "max_liquidity" => "Maximum Liquidity",
                    "sell_high" => "Sell High Percentage",
                    "sell_low" => "Sell Low Percentage",
                    _ => &setting,
                };

                let prompt_text = format!("Enter new value for <b>{}</b>\n\nCurrent value: <b>{}</b>\n\nBlockchain: <b>{}</b>",
                    setting_display,
                    current_value,
                    blockchain_str.to_uppercase()
                );

                bot_service.answer_callback_query(callback_id, None, false).await?;

                let prompt_msg = bot_service.send_message(chat_id, &prompt_text).await?;

                let prompt_msg_id = prompt_msg.id.0;

                let context = format!("config_setting:{}:{}:{}:{}", setting, blockchain_str, message_id, prompt_msg_id);
                let mut user_states = scan_screen::get_user_states().write().await;
                user_states.insert(chat_id, context);
            } else {
                bot_service.answer_callback_query(callback_id, Some("Invalid setting format"), false).await?;
            }
        }
        cmd if cmd.starts_with("refresh_trades_") => {
            // Handle refresh trades
            let blockchain_str = cmd.trim_start_matches("refresh_trades_");
            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Show the trades screen (which will refresh the trades)
            show_trades_screen(bot_service, user_data, message_id, blockchain).await?;

            bot_service.answer_callback_query(callback_id, Some(&format!("Refreshed {} trades", blockchain_str.to_uppercase())), false).await?;
        }
        "refresh_token" => {
            // Get token address and blockchain from JSON data
            let parsed_data: Value = serde_json::from_str(callback_data).unwrap();
            let token_address = parsed_data.get("address").and_then(|a| a.as_str()).unwrap_or("");
            let blockchain_str = parsed_data.get("blockchain").and_then(|b| b.as_str()).unwrap_or("");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Refresh token info
            handle_refresh_token(bot_service, callback_id, chat_id, message_id, token_address, &blockchain).await?;
        }
        "sell_token" => {
            // Get token address and blockchain from JSON data
            let parsed_data: Value = serde_json::from_str(callback_data).unwrap();
            let token_address = parsed_data.get("address").and_then(|a| a.as_str()).unwrap_or("");
            let blockchain_str = parsed_data.get("blockchain").and_then(|b| b.as_str()).unwrap_or("");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Show sell options
            handle_sell_token(bot_service, callback_id, chat_id, message_id, token_address, &blockchain).await?;
        }
        "chart_token" => {
            // Get token address and blockchain from JSON data
            let parsed_data: Value = serde_json::from_str(callback_data).unwrap();
            let token_address = parsed_data.get("address").and_then(|a| a.as_str()).unwrap_or("");
            let blockchain_str = parsed_data.get("blockchain").and_then(|b| b.as_str()).unwrap_or("");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Open chart
            handle_chart_token(bot_service, callback_id, token_address, &blockchain).await?;
        }
        "spend_token" => {
            // Get token address and blockchain from JSON data
            let parsed_data: Value = serde_json::from_str(callback_data).unwrap();
            let token_address = parsed_data.get("address").and_then(|a| a.as_str()).unwrap_or("");
            let blockchain_str = parsed_data.get("blockchain").and_then(|b| b.as_str()).unwrap_or("");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Show spend options
            handle_spend_token(bot_service, callback_id, chat_id, message_id, token_address, &blockchain).await?;
        }
        "sell_token_percent" => {
            // Get token address, blockchain, and percent from JSON data
            let parsed_data: Value = serde_json::from_str(callback_data).unwrap();
            let token_address = parsed_data.get("address").and_then(|a| a.as_str()).unwrap_or("");
            let blockchain_str = parsed_data.get("blockchain").and_then(|b| b.as_str()).unwrap_or("");
            let percent = parsed_data.get("percent").and_then(|p| p.as_u64()).unwrap_or(0);

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Get token info from cache if available
            let token_service = TokenInfoService::new();
            let token_info_opt = token_service.get_token_info_from_cache(token_address, &blockchain).await;

            // Build the confirmation message
            let mut confirm_text = format!(
                "✅ <b>Sell Order Submitted</b>\n\n"
            );

            // Add token info if available
            let token_name = if let Some(token_info) = token_info_opt {
                confirm_text.push_str(&format!("<b>{}</b> ({})\n", token_info.name, token_info.symbol));

                if token_info.price_usd > 0.0 {
                    confirm_text.push_str(&format!("💰 Price: ${:.8}\n", token_info.price_usd));
                }

                token_info.name.clone()
            } else {
                "Unknown".to_string()
            };

            // Add percentage and contract address
            if percent == 100 {
                confirm_text.push_str(&format!("\n🔢 Amount: All <b>{}</b> tokens (100%)\n", token_name));
            } else {
                confirm_text.push_str(&format!("\n🔢 Amount: {}% of <b>{}</b> tokens\n", percent, token_name));
            }

            confirm_text.push_str(&format!("📍 Contract: <code>{}</code>\n\n", token_address));
            confirm_text.push_str("This feature is not fully implemented yet. Your order has been recorded.");

            // Create the keyboard with back buttons
            let keyboard = InlineKeyboardMarkup::new(vec![
                vec![
                    InlineKeyboardButton::callback(
                        "◀️ Back to Token".to_string(),
                        format!("refresh_token:{}:{}", blockchain_str, token_address),
                    )
                ],
                vec![
                    InlineKeyboardButton::callback(
                        "🏠 Dashboard".to_string(),
                        format!("view_{}", blockchain_str),
                    ),
                    InlineKeyboardButton::callback(
                        "❌ Dismiss".to_string(),
                        "dismiss_message",
                    )
                ]
            ]);

            // Answer the callback query first
            bot_service.answer_callback_query(callback_id, None, false).await?;

            // Delete the original message
            bot_service.delete_message(chat_id, message_id).await?;

            // Send a new message with the confirmation
            bot_service.send_message_with_keyboard(chat_id, &confirm_text, keyboard).await?;
        }
        "sell_token_custom" => {
            // Get token address and blockchain from JSON data
            let parsed_data: Value = serde_json::from_str(callback_data).unwrap();
            let token_address = parsed_data.get("address").and_then(|a| a.as_str()).unwrap_or("");
            let blockchain_str = parsed_data.get("blockchain").and_then(|b| b.as_str()).unwrap_or("");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // This is now handled directly by the handle_sell_token function
            // Just forward to the main sell token handler
            handle_sell_token(bot_service, callback_id, chat_id, message_id, token_address, &blockchain).await?;
        }
        _ => {
            // Handle unknown command
            println!("Unknown JSON command: {}", command);
            bot_service.answer_callback_query(callback_id, Some("Unknown command"), false).await?;
        }
    }

    Ok(())
}

/// Handle string commands
async fn handle_string_command(
    bot_service: &BotService,
    callback_id: &str,
    chat_id: i64,
    message_id: i32,
    user_data: &UserData,
    command: &str,
    message_text: &str
) -> Result<(), BotError> {
    println!("Processing string command: {}", command);

    // Check for colon-separated commands (our new format)
    if command.contains(':') {
        let parts: Vec<&str> = command.split(':').collect();

        if parts.len() >= 2 {
            let cmd = parts[0];
            let blockchain_str = parts[1];

            // Special case for cancel_snipe
            if cmd == "cancel_snipe" && parts.len() >= 3 {
                let blockchain = match blockchain_str {
                    "bsc" => Blockchain::BSC,
                    "sol" => Blockchain::SOL,
                    "eth" => Blockchain::ETH,
                    "base" => Blockchain::BASE,
                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                };

                let contract_address = parts[2];

                // Get the snipe ID if provided
                let snipe_id = if parts.len() >= 4 && !parts[3].is_empty() {
                    match mongodb::bson::oid::ObjectId::parse_str(parts[3]) {
                        Ok(id) => Some(id),
                        Err(_) => None,
                    }
                } else {
                    None
                };

                // Snipe functionality has been removed
                let error_text = "Snipe functionality has been removed from this version.";
                bot_service.answer_callback_query(callback_id, Some(error_text), false).await?;

                return Ok(());
            }

            // Special case for confirm_new_wallet
            if cmd == "confirm_new_wallet" {
                let blockchain = match blockchain_str {
                    "bsc" => Blockchain::BSC,
                    "sol" => Blockchain::SOL,
                    "eth" => Blockchain::ETH,
                    "base" => Blockchain::BASE,
                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                };

                // Show generating message
                let generating_text = format!(
                    "🔄 <b>Generating New {} Wallet</b>\n\n\
                    Please wait while we generate your new wallet...",
                    blockchain_str.to_uppercase()
                );

                // Edit the message to show generating status
                bot_service.edit_message(chat_id, message_id, &generating_text).await?;

                // Answer the callback query
                bot_service.answer_callback_query(callback_id, None, false).await?;

                // Generate new wallet for the specific blockchain
                let blockchain_service = bot_service.blockchain_service();
                let new_wallet = blockchain_service.generate_wallet(blockchain.clone()).await?;

                // Get the user data
                let tg_user = bot_service.bot().get_chat_member(
                    teloxide::types::ChatId(chat_id),
                    teloxide::types::UserId(chat_id as u64)
                ).await?.user;
                let mut user_data = bot_service.user_service().get_or_create_user_data(chat_id, &tg_user).await?;

                // Update the wallet for the specific blockchain
                match blockchain {
                    Blockchain::BSC => user_data.wallets.bsc_wallet = new_wallet.clone(),
                    Blockchain::SOL => user_data.wallets.sol_wallet = new_wallet.clone(),
                    Blockchain::ETH => user_data.wallets.eth_wallet = new_wallet.clone(),
                    Blockchain::BASE => user_data.wallets.base_wallet = new_wallet.clone(),
                }

                // Save the updated wallets to the database
                crate::service::DbService::save_user_wallets(&user_data.wallets).await?;

                // Update in cache
                bot_service.user_service().cache_user_data(user_data.clone()).await;

                // Show success message with the new wallet
                let success_text = format!(
                    "✅ <b>New {} Wallet Generated</b>\n\n\
                    Your new wallet has been generated successfully.\n\n\
                    <b>Address:</b> <code>{}</code>\n\n\
                    <b>Private Key:</b> <tg-spoiler><code>{}</code></tg-spoiler>\n\n\
                    <b>⚠️ IMPORTANT:</b> Save this private key in a secure location. It will not be shown again!",
                    blockchain_str.to_uppercase(),
                    new_wallet.address,
                    new_wallet.private_key
                );

                // Create keyboard with back button
                let keyboard = InlineKeyboardMarkup::new(vec![
                    vec![
                        InlineKeyboardButton::callback(
                            "◀️ Back to Dashboard".to_string(),
                            format!("view_{}", blockchain_str),
                        ),
                    ],
                ]);

                // Edit the message with the success text and keyboard
                bot_service.edit_message_with_keyboard(chat_id, message_id, &success_text, keyboard).await?;

                // Show a brief notification
                bot_service.answer_callback_query(callback_id, Some(&format!("New {} wallet generated successfully", blockchain_str.to_uppercase())), false).await?;

                return Ok(());
            }

            // For token operations, we need the token address
            let token_address = if parts.len() >= 3 { parts[2] } else { "" };

            // For sell_token_percent, we need the percentage
            let percent = if parts.len() >= 4 && cmd == "sell_token_percent" {
                parts[3].parse::<u64>().unwrap_or(0)
            } else {
                0
            };

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            match cmd {
                "refresh_token" => {
                    handle_refresh_token(bot_service, callback_id, chat_id, message_id, token_address, &blockchain).await?;
                    return Ok(());
                },
                "sell_token" => {
                    handle_sell_token(bot_service, callback_id, chat_id, message_id, token_address, &blockchain).await?;
                    return Ok(());
                },
                "chart_token" => {
                    handle_chart_token(bot_service, callback_id, token_address, &blockchain).await?;
                    return Ok(());
                },
                "spend_token" => {
                    handle_spend_token(bot_service, callback_id, chat_id, message_id, token_address, &blockchain).await?;
                    return Ok(());
                },
                "sell_token_percent" => {
                    // Get token info from cache if available
                    let token_service = TokenInfoService::new();
                    let token_info_opt = token_service.get_token_info_from_cache(token_address, &blockchain).await;

                    // Build the confirmation message
                    let mut confirm_text = format!(
                        "✅ <b>Sell Order Submitted</b>\n\n"
                    );

                    // Add token info if available
                    let token_name = if let Some(token_info) = token_info_opt {
                        confirm_text.push_str(&format!("<b>{}</b> ({})\n", token_info.name, token_info.symbol));

                        if token_info.price_usd > 0.0 {
                            confirm_text.push_str(&format!("💰 Price: ${:.8}\n", token_info.price_usd));
                        }

                        token_info.name.clone()
                    } else {
                        "Unknown".to_string()
                    };

                    // Add percentage and contract address
                    if percent == 100 {
                        confirm_text.push_str(&format!("\n🔢 Amount: All <b>{}</b> tokens (100%)\n", token_name));
                    } else {
                        confirm_text.push_str(&format!("\n🔢 Amount: {}% of <b>{}</b> tokens\n", percent, token_name));
                    }

                    confirm_text.push_str(&format!("📍 Contract: <code>{}</code>\n\n", token_address));
                    confirm_text.push_str("This feature is not fully implemented yet. Your order has been recorded.");

                    // Create the keyboard with back buttons
                    let keyboard = InlineKeyboardMarkup::new(vec![
                        vec![
                            InlineKeyboardButton::callback(
                                "◀️ Back to Token".to_string(),
                                format!("refresh_token:{}:{}", blockchain.as_str(), token_address),
                            )
                        ],
                        vec![
                            InlineKeyboardButton::callback(
                                "🏠 Dashboard".to_string(),
                                format!("view_{}", blockchain.as_str()),
                            ),
                            InlineKeyboardButton::callback(
                                "❌ Dismiss".to_string(),
                                "dismiss_message",
                            )
                        ]
                    ]);

                    // Answer the callback query first
                    bot_service.answer_callback_query(callback_id, None, false).await?;

                    // Delete the original message
                    bot_service.delete_message(chat_id, message_id).await?;

                    // Send a new message with the confirmation
                    bot_service.send_message_with_keyboard(chat_id, &confirm_text, keyboard).await?;

                    return Ok(());
                },
                "sell_token_custom" => {
                    // This is now handled directly by the handle_sell_token function
                    // Just forward to the main sell token handler
                    handle_sell_token(bot_service, callback_id, chat_id, message_id, token_address, &blockchain).await?;
                    return Ok(());
                },
                "buy_token" => {
                    // Get the amount from parts[3] if available
                    let amount = if parts.len() >= 4 {
                        parts[3].parse::<f64>().unwrap_or(0.1)
                    } else {
                        0.1 // Default amount
                    };

                    // Get user data
                    let user_data = bot_service.get_or_create_user(chat_id, &bot_service.bot().get_chat_member(
                        teloxide::types::ChatId(chat_id),
                        teloxide::types::UserId(chat_id as u64)
                    ).await?.user).await?;

                    // Use the chain-specific implementation
                    match blockchain {
                        Blockchain::BSC => {
                            // Execute buy token using the BSC implementation
                            crate::screens::bsc_screen::execute_buy_token(
                                bot_service,
                                &user_data,
                                message_id,
                                token_address,
                                amount
                            ).await?;
                        },
                        Blockchain::ETH => {
                            // Execute buy token using the ETH implementation
                            crate::screens::eth_screen::execute_buy_token(
                                bot_service,
                                &user_data,
                                message_id,
                                token_address,
                                amount
                            ).await?;
                        },
                        Blockchain::BASE => {
                            // Execute buy token using the BASE implementation
                            crate::screens::base_screen::execute_buy_token(
                                bot_service,
                                &user_data,
                                message_id,
                                token_address,
                                amount
                            ).await?;
                        },
                        _ => {
                            // For other blockchains, use the existing implementation
                            // This is handled in the message handler
                        }
                    }

                    // Answer the callback query
                    bot_service.answer_callback_query(callback_id, None, false).await?;
                    return Ok(());
                },
                "buy_token_custom" => {
                    // This command is no longer needed since we're using the force reply pattern
                    // Just forward to the regular buy token handler

                    // Get user data
                    let user_data = bot_service.get_or_create_user(chat_id, &bot_service.bot().get_chat_member(
                        teloxide::types::ChatId(chat_id),
                        teloxide::types::UserId(chat_id as u64)
                    ).await?.user).await?;

                    // Use the chain-specific implementation
                    match blockchain {
                        Blockchain::BSC => {
                            // Show buy token screen with force reply
                            crate::screens::bsc_screen::show_buy_token_screen(
                                bot_service,
                                &user_data,
                                message_id,
                                token_address
                            ).await?;
                        },
                        Blockchain::ETH => {
                            // Show buy token screen with force reply
                            // For now, we'll use the same implementation as BSC
                            crate::screens::bsc_screen::show_buy_token_screen(
                                bot_service,
                                &user_data,
                                message_id,
                                token_address
                            ).await?;
                        },
                        Blockchain::BASE => {
                            // Show buy token screen with force reply
                            // For now, we'll use the same implementation as BSC
                            crate::screens::bsc_screen::show_buy_token_screen(
                                bot_service,
                                &user_data,
                                message_id,
                                token_address
                            ).await?;
                        },
                        _ => {
                            // For other blockchains, use the existing implementation
                            // This is handled in the message handler
                        }
                    }

                    // Answer the callback query
                    bot_service.answer_callback_query(callback_id, None, false).await?;
                    return Ok(());
                },


                _ => {}
            }
        }
    }

    match command {
        "help" => {
            // Handle help callback
            let help_text = "\
                <b>Available Commands:</b>\n\n\
                /start - Start the bot\n\
                /help - Show this help message\n\
                /about - About this bot\
            ";
            bot_service.edit_message(chat_id, message_id, help_text).await?;
            bot_service.answer_callback_query(callback_id, Some("Help information shown"), false).await?;
        }
        "about" => {
            // Handle about callback
            let about_text = "\
                <b>About EasyBot</b>\n\n\
                EasyBot is a Telegram trading bot.\n\n\
                Version: 0.1.0\
            ";
            bot_service.edit_message(chat_id, message_id, about_text).await?;
            bot_service.answer_callback_query(callback_id, Some("About information shown"), false).await?;
        }
        // Dashboard view handlers
        cmd if cmd.starts_with("dashboard_") => {
            // Handle dashboard view changes: dashboard_{blockchain}_{view}
            let parts: Vec<&str> = cmd.trim_start_matches("dashboard_").split('_').collect();
            if parts.len() == 2 {
                let blockchain_str = parts[0];
                let view_str = parts[1];

                let blockchain = match blockchain_str {
                    "bsc" => Blockchain::BSC,
                    "sol" => Blockchain::SOL,
                    "eth" => Blockchain::ETH,
                    "base" => Blockchain::BASE,
                    _ => {
                        bot_service.answer_callback_query(callback_id, Some("Invalid blockchain"), false).await?;
                        return Ok(());
                    }
                };

                let view = match view_str {
                    "overview" => DashboardView::Overview,
                    "rewards" => DashboardView::Rewards,
                    "settings" => DashboardView::Settings,
                    "trading" => DashboardView::Trading,
                    "wallets" => DashboardView::Wallets,
                    _ => {
                        bot_service.answer_callback_query(callback_id, Some("Invalid view"), false).await?;
                        return Ok(());
                    }
                };

                // Use dashboard state service for seamless transitions
                let dashboard_state = DashboardStateService::new();
                dashboard_state.transition_to_view(
                    bot_service,
                    &user_data,
                    view,
                    Some(blockchain)
                ).await?;

                let view_name = match view {
                    DashboardView::Overview => "Overview",
                    DashboardView::Rewards => "Rewards",
                    DashboardView::Settings => "Settings",
                    DashboardView::Trading => "Trading",
                    DashboardView::Wallets => "Wallets",
                };

                bot_service.answer_callback_query(
                    callback_id,
                    Some(&format!("Showing {} {}", blockchain_str.to_uppercase(), view_name)),
                    false
                ).await?;
            }
        }
        // Blockchain switching handlers (for backward compatibility)
        cmd if cmd.starts_with("switch_") => {
            // Handle switch_{blockchain} commands
            let blockchain_str = cmd.trim_start_matches("switch_");
            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => {
                    bot_service.answer_callback_query(callback_id, Some("Invalid blockchain"), false).await?;
                    return Ok(());
                }
            };

            // Use dashboard state service to preserve current view
            let dashboard_state = DashboardStateService::new();
            dashboard_state.switch_blockchain_preserve_view(bot_service, &user_data, blockchain).await?;

            bot_service.answer_callback_query(
                callback_id,
                Some(&format!("Switched to {}", blockchain_str.to_uppercase())),
                false
            ).await?;
        }
        "switch_blockchain_bsc" | "view_bsc" => {
            // Switch to BSC blockchain
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::BSC).await?;

            if command == "switch_blockchain_bsc" {
                // Delete the current message and show fresh dashboard
                bot_service.delete_message(chat_id, message_id).await?;
                show_fresh_dashboard(bot_service, &updated_user_data).await?;
            } else {
                // Update the current message with the dashboard
                show_dashboard(bot_service, &updated_user_data, message_id).await?;
            }

            bot_service.answer_callback_query(callback_id, Some("Switched to BSC"), false).await?;
        }
        "switch_blockchain_sol" | "view_sol" => {
            // Switch to SOL blockchain
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::SOL).await?;

            if command == "switch_blockchain_sol" {
                // Delete the current message and show fresh dashboard
                bot_service.delete_message(chat_id, message_id).await?;
                show_fresh_dashboard(bot_service, &updated_user_data).await?;
            } else {
                // Update the current message with the dashboard
                show_dashboard(bot_service, &updated_user_data, message_id).await?;
            }

            bot_service.answer_callback_query(callback_id, Some("Switched to Solana"), false).await?;
        }
        "switch_blockchain_eth" | "view_eth" => {
            // Switch to ETH blockchain
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::ETH).await?;

            if command == "switch_blockchain_eth" {
                // Delete the current message and show fresh dashboard
                bot_service.delete_message(chat_id, message_id).await?;
                show_fresh_dashboard(bot_service, &updated_user_data).await?;
            } else {
                // Update the current message with the dashboard
                show_dashboard(bot_service, &updated_user_data, message_id).await?;
            }

            bot_service.answer_callback_query(callback_id, Some("Switched to Ethereum"), false).await?;
        }
        "switch_blockchain_base" | "view_base" => {
            // Switch to BASE blockchain
            let updated_user_data = bot_service.user_service().update_user_blockchain(chat_id, Blockchain::BASE).await?;

            if command == "switch_blockchain_base" {
                // Delete the current message and show fresh dashboard
                bot_service.delete_message(chat_id, message_id).await?;
                show_fresh_dashboard(bot_service, &updated_user_data).await?;
            } else {
                // Update the current message with the dashboard
                show_dashboard(bot_service, &updated_user_data, message_id).await?;
            }

            bot_service.answer_callback_query(callback_id, Some("Switched to Base"), false).await?;
        }
        "dismiss_message" => {
            // Delete the message
            bot_service.delete_message(chat_id, message_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Message dismissed"), false).await?;
        }
        "dashboard" => {
            // Show main dashboard
            show_fresh_dashboard(bot_service, user_data).await?;
            bot_service.answer_callback_query(callback_id, Some("Showing dashboard"), false).await?;
        }
        "rewards_dashboard" => {
            crate::screens::rewards::show_rewards_dashboard(bot_service, chat_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Showing rewards dashboard"), false).await?;
        }
        "rewards_refresh" => {
            crate::screens::rewards::show_rewards_dashboard(bot_service, chat_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Rewards refreshed"), false).await?;
        }
        "rewards_leaderboard" => {
            crate::screens::rewards::show_rewards_leaderboard(bot_service, chat_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Showing leaderboard"), false).await?;
        }
        "rewards_info" => {
            crate::screens::rewards::show_rewards_info(bot_service, chat_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Showing rewards info"), false).await?;
        }
        "profile_simple" => {
            crate::screens::user_profile::handle_profile_view_toggle(bot_service, user_data, message_id, false).await?;
            bot_service.answer_callback_query(callback_id, Some("Showing simple profile"), false).await?;
        }
        "profile_detailed" => {
            crate::screens::user_profile::handle_profile_view_toggle(bot_service, user_data, message_id, true).await?;
            bot_service.answer_callback_query(callback_id, Some("Showing detailed profile"), false).await?;
        }
        "profile_refresh" => {
            // Determine current view mode from message content
            let detailed_view = message_text.contains("Rewards by Blockchain:");
            crate::screens::user_profile::handle_profile_refresh(bot_service, user_data, message_id, detailed_view).await?;
            bot_service.answer_callback_query(callback_id, Some("Profile refreshed"), false).await?;
        }
        "back_to_last_token" => {
            println!("🔍 DEBUG: Processing back_to_last_token callback");
            println!("🔍 DEBUG: Message text: {}", message_text);

            // Extract token address from the message text (look for Contract: ADDRESS)
            let token_address = if let Some(start) = message_text.find("Contract:") {
                let contract_part = &message_text[start + 9..]; // Skip "Contract:"
                let contract_part = contract_part.trim(); // Remove whitespace

                // Find the first line that contains the contract address
                if let Some(newline_pos) = contract_part.find('\n') {
                    let address_line = &contract_part[..newline_pos].trim();
                    // Check if this looks like a valid contract address (alphanumeric, reasonable length)
                    if address_line.len() >= 32 && address_line.chars().all(|c| c.is_alphanumeric()) {
                        Some(*address_line)
                    } else {
                        None
                    }
                } else {
                    // No newline, check if the rest is a valid address
                    let address_candidate = contract_part.trim();
                    if address_candidate.len() >= 32 && address_candidate.chars().all(|c| c.is_alphanumeric()) {
                        Some(address_candidate)
                    } else {
                        None
                    }
                }
            } else {
                None
            };

            println!("🔍 DEBUG: Extracted token address: {:?}", token_address);

            if let Some(token_addr) = token_address {
                // Determine blockchain from user's current blockchain or default to SOL
                let blockchain = user_data.user.current_blockchain.clone();

                println!("🔍 DEBUG: Using blockchain: {}, token: {}", blockchain.as_str(), token_addr);

                // Answer callback query first
                bot_service.answer_callback_query(callback_id, Some("Loading token information from DexScreener..."), false).await?;

                // Call DexScreener scan directly
                handle_refresh_token_new_message(bot_service, callback_id, chat_id, token_addr, &blockchain).await?;
                return Ok(());
            }

            println!("🔍 DEBUG: Could not extract token address from message");
            bot_service.answer_callback_query(callback_id, Some("Could not find token information"), false).await?;
        }
        cmd if cmd.starts_with("scan_contract_") => {
            // Handle scan contract with specific blockchain
            let blockchain_str = cmd.trim_start_matches("scan_contract_");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Answer the callback query FIRST to prevent timeout
            bot_service.answer_callback_query(callback_id, Some(&format!("Scanning {} contract", blockchain_str.to_uppercase())), false).await?;

            // Get the user data
            let tg_user = bot_service.bot().get_chat_member(
                teloxide::types::ChatId(chat_id),
                teloxide::types::UserId(chat_id as u64)
            ).await?.user;

            // Update the user's current blockchain
            let mut user_data_mut = bot_service.user_service().get_or_create_user_data(chat_id, &tg_user).await?;
            user_data_mut.set_current_blockchain(blockchain.clone());
            bot_service.user_service().cache_user_data(user_data_mut.clone()).await;

            // Save to database
            crate::service::DbService::save_user(&user_data_mut.user).await?;

            // Show scan prompt with the specific blockchain
            scan_screen::show_scan_prompt_with_blockchain(bot_service, &user_data_mut, message_id, &blockchain).await?;
        }
        cmd if cmd.starts_with("rewards_history_") => {
            // Handle rewards history for specific user
            let user_id_str = cmd.trim_start_matches("rewards_history_");
            let user_id = user_id_str.parse::<i64>().unwrap_or(chat_id);

            crate::screens::rewards::show_rewards_history(bot_service, chat_id, user_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Showing reward history"), false).await?;
        }
        cmd if cmd.starts_with("sniper_") => {
            // Sniper functionality has been removed
            bot_service.answer_callback_query(callback_id, Some("Sniper functionality is no longer available"), false).await?;
        }

        cmd if cmd.starts_with("sniper_token_") => {
            // Handle snipe token
            let blockchain_str = cmd.trim_start_matches("sniper_token_");
            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Answer the callback query FIRST to prevent timeout
            bot_service.answer_callback_query(callback_id, Some(&format!("Setting up sniper for {}", blockchain_str.to_uppercase())), false).await?;

            // Get the user data
            let tg_user = bot_service.bot().get_chat_member(
                teloxide::types::ChatId(chat_id),
                teloxide::types::UserId(chat_id as u64)
            ).await?.user;

            // Update the user's current blockchain
            let mut user_data_mut = bot_service.user_service().get_or_create_user_data(chat_id, &tg_user).await?;
            user_data_mut.set_current_blockchain(blockchain.clone());
            bot_service.user_service().cache_user_data(user_data_mut.clone()).await;

            // Save to database
            crate::service::DbService::save_user(&user_data_mut.user).await?;

            // Show the snipe token prompt
            let snipe_text = format!(
                "🎯 <b>{} Token Sniper</b>\n\n\
                Enter the contract address of the token you want to snipe:",
                blockchain.to_string().to_uppercase()
            );

            // Create a force reply to get the contract address
            let force_reply = teloxide::types::ForceReply::new();

            // Delete the current message
            bot_service.delete_message(chat_id, message_id).await?;

            // Send the new message with force reply
            let sent_msg = bot_service.bot().send_message(
                teloxide::types::ChatId(chat_id),
                snipe_text
            )
            .reply_markup(force_reply)
            .parse_mode(teloxide::types::ParseMode::Html)
            .await?;

            // Store the context for handling the reply
            let context = format!("snipe_token:{}:{}", blockchain.as_str(), sent_msg.id.0);
            let mut user_states = scan_screen::get_user_states().write().await;
            user_states.insert(chat_id, context);
        }
        // Handle snipe config commands
        cmd if cmd.starts_with("set_snipe_amount:") ||
             cmd.starts_with("set_snipe_slippage:") ||
             cmd.starts_with("set_snipe_gas_price:") ||
             cmd.starts_with("set_snipe_gas_limit:") ||
             cmd.starts_with("set_snipe_sell_high:") ||
             cmd.starts_with("set_snipe_sell_low:") => {

            // Parse the command to get the setting, blockchain, and token address
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() < 3 {
                return Err(BotError::user_error("Invalid command format".to_string()));
            }

            let setting = match parts[0] {
                "set_snipe_amount" => "amount",
                "set_snipe_slippage" => "slippage",
                "set_snipe_gas_price" => "gas_price",
                "set_snipe_gas_limit" => "gas_limit",
                "set_snipe_sell_high" => "sell_high",
                "set_snipe_sell_low" => "sell_low",
                _ => "unknown",
            };

            let blockchain_str = parts[1];
            let shortened_token = parts[2];

            // Get the full token address from the user's config
            let token_address = match blockchain_str {
                "bsc" => user_data.config.bsc_current_contract_address.clone(),
                "sol" => user_data.config.sol_current_contract_address.clone(),
                "eth" => user_data.config.eth_current_contract_address.clone(),
                "base" => user_data.config.base_current_contract_address.clone(),
                _ => None,
            }.unwrap_or_else(|| shortened_token.to_string());

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Get the current value for display
            let current_value = match setting {
                "amount" => "0.1".to_string(), // Default amount
                "slippage" => format!("{}%", user_data.get_config(&blockchain).map_or("1.0".to_string(), |c| c.slippage.to_string())),
                "gas_price" => format!("{} Gwei", user_data.get_config(&blockchain).map_or("5.0".to_string(), |c| c.max_gas_price.to_string())),
                "gas_limit" => format!("{}", user_data.get_config(&blockchain).map_or("250000".to_string(), |c| c.max_gas_limit.to_string())),
                "sell_high" => format!("{}%", user_data.get_config(&blockchain).map_or("20.0".to_string(), |c| c.sell_high.to_string())),
                "sell_low" => format!("{}%", user_data.get_config(&blockchain).map_or("5.0".to_string(), |c| c.sell_low.to_string())),
                _ => "Unknown".to_string(),
            };

            // Format the setting name for display
            let setting_display = match setting {
                "amount" => "Snipe Amount",
                "slippage" => "Slippage",
                "gas_price" => "Max Gas Price",
                "gas_limit" => "Gas Limit",
                "sell_high" => "Sell High Percentage",
                "sell_low" => "Sell Low Percentage",
                _ => setting,
            };

            // Ask the user for the new value with force reply
            let prompt_text = format!("Enter new value for <b>{}</b>\n\nCurrent value: <b>{}</b>\n\nBlockchain: <b>{}</b>\nToken: <code>{}</code>",
                setting_display,
                current_value,
                blockchain_str.to_uppercase(),
                token_address
            );

            // Delete any previous message to keep the chat clean
            bot_service.answer_callback_query(callback_id, None, false).await?;

            // Send a new message with the prompt
            let prompt_msg = bot_service.send_message(chat_id, &prompt_text).await?;

            // Store the prompt message ID to delete it later
            let prompt_msg_id = prompt_msg.id.0;

            // Store the context for handling the reply
            let context = format!("snipe_setting:{}:{}:{}:{}:{}", setting, blockchain_str, token_address, message_id, prompt_msg_id);
            let mut user_states = scan_screen::get_user_states().write().await;
            user_states.insert(chat_id, context);
        }

        // Handle security info
        cmd if cmd.starts_with("security_info:") => {
            // Parse the command to get the blockchain and token address
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() < 3 {
                return Err(BotError::user_error("Invalid command format".to_string()));
            }

            let blockchain_str = parts[1];
            let token_address = parts[2];

            // Show a message about security features
            bot_service.answer_callback_query(
                callback_id,
                Some("🛡️ Anti-Rug and Anti-MEV protection are always enabled for your safety. These features help protect against common scams and front-running attacks."),
                true
            ).await?;
        }

        // Handle toggle snipe settings (legacy, kept for backward compatibility)
        cmd if cmd.starts_with("toggle_snipe_auto_approve:") ||
             cmd.starts_with("toggle_snipe_auto_sell:") => {

            // Parse the command to get the setting, blockchain, and token address
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() < 3 {
                return Err(BotError::user_error("Invalid command format".to_string()));
            }

            let setting = match parts[0] {
                "toggle_snipe_antirug" => "antirug",
                "toggle_snipe_antimev" => "antimev",
                "toggle_snipe_auto_approve" => "auto_approve",
                "toggle_snipe_auto_sell" => "auto_sell",
                _ => "unknown",
            };

            let blockchain_str = parts[1];
            let shortened_token = parts[2];

            // Get the full token address from the user's config
            let token_address = match blockchain_str {
                "bsc" => user_data.config.bsc_current_contract_address.clone(),
                "sol" => user_data.config.sol_current_contract_address.clone(),
                "eth" => user_data.config.eth_current_contract_address.clone(),
                "base" => user_data.config.base_current_contract_address.clone(),
                _ => None,
            }.unwrap_or_else(|| shortened_token.to_string());

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Get a copy of user data to update
            let user_data_mut = user_data.clone();

            // Get the snipe from the database
            let snipes = crate::service::DbService::get_user_snipes_by_blockchain(chat_id, &blockchain).await?;

            // Find the snipe with the matching token address (case-insensitive)
            let mut snipe_opt = None;
            for s in snipes {
                if s.contract_address.to_lowercase() == token_address.to_lowercase() {
                    snipe_opt = Some(s);
                    break;
                }
            }

            let mut snipe = if let Some(s) = snipe_opt {
                // Use existing snipe
                s
            } else {
                // Create a new snipe with default values
                let config = user_data_mut.get_config(&blockchain).cloned().unwrap_or_else(|| {
                    match blockchain {
                        Blockchain::BSC => crate::model::wallet_config::WalletConfig::bsc_default(),
                        Blockchain::SOL => crate::model::wallet_config::WalletConfig::sol_default(),
                        Blockchain::ETH => crate::model::wallet_config::WalletConfig::eth_default(),
                        Blockchain::BASE => crate::model::wallet_config::WalletConfig::base_default(),
                    }
                });

                // Create a new snipe with default values
                let amount = mongodb::bson::Decimal128::from_str("0.1").unwrap();
                crate::model::Snipe::new(
                    chat_id,
                    blockchain.clone(),
                    token_address.to_string(),
                    amount,
                    config.slippage,
                    config.antirug,
                    config.anti_mev
                )
            };

            // Toggle the setting
            match setting {
                "antirug" => snipe.antirug = !snipe.antirug,
                "antimev" => snipe.anti_mev = !snipe.anti_mev,
                _ => return Err(BotError::user_error(format!("Unknown setting: {}", setting))),
            }

            // Save the snipe to the database
            if snipe.id.is_some() {
                // Update existing snipe
                crate::service::DbService::save_snipe(&mut snipe).await?;
            } else {
                // Add new snipe
                crate::service::DbService::add_snipe_to_user(chat_id, &blockchain, &mut snipe).await?;
            }

            // Show the updated snipe config
            crate::screens::snipe_config_screen::show_snipe_config_screen(
                bot_service,
                &user_data_mut,
                message_id,
                blockchain,
                &token_address
            ).await?;

            // Success message is shown in the updated config screen
            bot_service.answer_callback_query(callback_id, Some("✅ Setting updated"), false).await?;
        }

        // Handle execute snipe
        cmd if cmd.starts_with("execute_snipe:") => {
            // Parse the command to get the blockchain and token address
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() < 3 {
                return Err(BotError::user_error("Invalid command format".to_string()));
            }

            let blockchain_str = parts[1];
            let shortened_token = parts[2];

            // Get the full token address from the user's config
            let token_address = match blockchain_str {
                "bsc" => user_data.config.bsc_current_contract_address.clone(),
                "sol" => user_data.config.sol_current_contract_address.clone(),
                "eth" => user_data.config.eth_current_contract_address.clone(),
                "base" => user_data.config.base_current_contract_address.clone(),
                _ => None,
            }.unwrap_or_else(|| shortened_token.to_string());

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Show executing message
            let executing_text = format!(
                "🎯 <b>Executing Snipe</b>\n\n\
                Blockchain: <b>{}</b>\n\
                Token: <code>{}</code>\n\n\
                Please wait while we set up the snipe...",
                blockchain.to_string().to_uppercase(),
                token_address
            );

            // Edit the message to show executing status
            bot_service.edit_message(chat_id, message_id, &executing_text).await?;

            // Answer the callback query
            bot_service.answer_callback_query(callback_id, Some("Setting up snipe..."), false).await?;

            // First, check if a snipe already exists for this token in the database
            println!("CRITICAL: Checking for existing snipe for token: {}", token_address);
            let snipes = crate::service::DbService::get_user_snipes_by_blockchain(chat_id, &blockchain).await?;

            let mut existing_snipe = None;
            for s in &snipes {
                if s.contract_address.to_lowercase() == token_address.to_lowercase() {
                    existing_snipe = Some(s.clone());
                    println!("CRITICAL: Found existing snipe with ID: {:?}", s.id);
                    break;
                }
            }

            // Get the user's config for this blockchain
            let config = user_data.get_config(&blockchain).cloned().unwrap_or_else(|| {
                match blockchain {
                    Blockchain::BSC => crate::model::wallet_config::WalletConfig::bsc_default(),
                    Blockchain::SOL => crate::model::wallet_config::WalletConfig::sol_default(),
                    Blockchain::ETH => crate::model::wallet_config::WalletConfig::eth_default(),
                    Blockchain::BASE => crate::model::wallet_config::WalletConfig::base_default(),
                }
            });

            // Use the default amount and slippage from the wallet config
            // Always set antirug and anti_mev to true for security
            let amount = mongodb::bson::Decimal128::from_str("0.1").unwrap();
            let slippage = config.slippage;
            let antirug = true; // Always enabled
            let anti_mev = true; // Always enabled

            // Create or update the snipe
            let (mut snipe, is_new) = if let Some(mut s) = existing_snipe {
                // Update the existing snipe
                println!("CRITICAL: Updating existing snipe for token: {}", token_address);
                s.status = crate::model::SnipeStatus::Pending; // Reset status to pending
                s.updated_at = chrono::Utc::now(); // Update timestamp

                // Update with cached values if available
                s.amount = amount;
                s.slippage = slippage;
                s.antirug = antirug;
                s.anti_mev = anti_mev;

                (s, false)
            } else {
                // Create a new snipe
                println!("CRITICAL: Creating new snipe for token: {}", token_address);
                let new_snipe = crate::model::Snipe::new(
                    chat_id,
                    blockchain.clone(),
                    token_address.to_string(),
                    amount,
                    slippage,
                    antirug,
                    anti_mev
                );
                (new_snipe, true)
            };

            // Save the snipe to the database
            if is_new {
                println!("CRITICAL: Saving new snipe to database");
                crate::service::DbService::add_snipe_to_user(chat_id, &blockchain, &mut snipe).await?;
            } else if let Some(id) = snipe.id {
                println!("CRITICAL: Updating existing snipe in database with ID: {}", id);
                crate::service::DbService::save_snipe(&mut snipe).await?;
            }

            // Show success message
            let success_text = format!(
                "✅ <b>Snipe {} Successfully</b>\n\n\
                Blockchain: <b>{}</b>\n\
                Token: <code>{}</code>\n\n\
                <b>Settings:</b>\n\
                • Amount: {} {}\n\
                • Slippage: {}%\n\
                • Anti-Rug: {}\n\
                • Anti-MEV: {}\n\n\
                Your snipe has been saved and will execute when liquidity is added.",
                if is_new { "Set Up" } else { "Updated" },
                blockchain.to_string().to_uppercase(),
                token_address,
                snipe.amount.to_string(),
                blockchain.get_native_symbol(),
                snipe.slippage.to_string(),
                "✅ Enabled", // Always enabled
                "✅ Enabled" // Always enabled
            );

            // Create keyboard with back buttons
            let keyboard = InlineKeyboardMarkup::new(vec![
                vec![
                    InlineKeyboardButton::callback(
                        "◀️ Back to Sniper Dashboard".to_string(),
                        format!("sniper_dashboard_{}", blockchain.as_str()),
                    ),
                ],
                vec![
                    InlineKeyboardButton::callback(
                        "🏠 Main Dashboard".to_string(),
                        format!("view_{}", blockchain.as_str()),
                    ),
                ],
            ]);

            // Edit the message with the success text and keyboard
            bot_service.edit_message_with_keyboard(chat_id, message_id, &success_text, keyboard).await?;
        }
        cmd if cmd.starts_with("show_snipe_config:") => {
            // Parse the command to get the blockchain and token address
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() < 3 {
                return Err(BotError::user_error("Invalid command format".to_string()));
            }

            let blockchain_str = parts[1];
            let shortened_token = parts[2];

            // Get the full token address from the user's config
            let token_address = match blockchain_str {
                "bsc" => user_data.config.bsc_current_contract_address.clone(),
                "sol" => user_data.config.sol_current_contract_address.clone(),
                "eth" => user_data.config.eth_current_contract_address.clone(),
                "base" => user_data.config.base_current_contract_address.clone(),
                _ => None,
            }.unwrap_or_else(|| shortened_token.to_string());

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Answer the callback query FIRST to prevent timeout
            bot_service.answer_callback_query(callback_id, Some(&format!("Loading snipe config for {}", blockchain_str.to_uppercase())), false).await?;

            // Get the user data
            let tg_user = bot_service.bot().get_chat_member(
                teloxide::types::ChatId(chat_id),
                teloxide::types::UserId(chat_id as u64)
            ).await?.user;

            // Get user data
            let user_data = bot_service.user_service().get_or_create_user_data(chat_id, &tg_user).await?;

            // Show the snipe config screen
            crate::screens::snipe_config_screen::show_snipe_config_screen(
                bot_service,
                &user_data,
                message_id,
                blockchain,
                &token_address
            ).await?;
        }

        cmd if cmd.starts_with("snipe_next:") || cmd.starts_with("snipe_prev:") => {
            // Parse the command to get the blockchain and current index
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() < 3 {
                return Err(BotError::user_error("Invalid command format".to_string()));
            }

            let is_next = parts[0] == "snipe_next";
            let blockchain_str = parts[1];
            let current_index = parts[2].parse::<usize>().unwrap_or(0);

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Answer the callback query FIRST to prevent timeout
            bot_service.answer_callback_query(callback_id, Some("Navigating snipes..."), false).await?;

            // Get the user data
            let tg_user = bot_service.bot().get_chat_member(
                teloxide::types::ChatId(chat_id),
                teloxide::types::UserId(chat_id as u64)
            ).await?.user;

            // Fetch active snipes from the database
            let snipes = crate::service::DbService::get_user_snipes_by_blockchain(chat_id, &blockchain).await?;

            if snipes.is_empty() {
                // No snipes to navigate
                return Err(BotError::user_error("No snipes found".to_string()));
            }

            // Calculate the new index
            let new_index = if is_next {
                (current_index + 1) % snipes.len()
            } else {
                if current_index == 0 {
                    snipes.len() - 1
                } else {
                    current_index - 1
                }
            };

            // Format the snipes
            let mut text = format!(
                "👁️ <b>Active {} Snipes</b>\n\n\
                You have {} active snipe(s) on {} blockchain:\n\n",
                blockchain.to_string().to_uppercase(),
                snipes.len(),
                blockchain.to_string().to_uppercase()
            );

            // Show the selected snipe
            let snipe = &snipes[new_index];
            let status_str = match snipe.status {
                crate::model::SnipeStatus::Pending => "⏳ Pending",
                crate::model::SnipeStatus::Executed => "✅ Executed",
                crate::model::SnipeStatus::Completed => "✅ Completed",
                crate::model::SnipeStatus::Failed => "❌ Failed",
                crate::model::SnipeStatus::Cancelled => "🚫 Cancelled",
            };

            text.push_str(&format!(
                "<b>Snipe #{}/{}</b>\n\
                Token: <code>{}</code>\n\
                Amount: {} {}\n\
                Slippage: {}%\n\
                Anti-Rug: {}\n\
                Anti-MEV: {}\n\
                Status: {}\n\
                Created: {}\n",
                new_index + 1,
                snipes.len(),
                snipe.contract_address,
                snipe.amount.to_string(),
                blockchain.get_native_symbol(),
                snipe.slippage.to_string(),
                "✅ Enabled", // Always enabled
                "✅ Enabled", // Always enabled
                status_str,
                snipe.created_at.format("%Y-%m-%d %H:%M:%S")
            ));

            // Create keyboard with navigation and CRUD buttons
            let mut keyboard_rows = Vec::new();

            // Add navigation buttons
            let snipe_id = snipe.id.unwrap().to_hex();

            // Add navigation buttons if there are multiple snipes
            if snipes.len() > 1 {
                keyboard_rows.push(vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "⬅️ Previous".to_string(),
                        format!("snipe_prev:{}:{}", blockchain.as_str(), new_index)
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "➡️ Next".to_string(),
                        format!("snipe_next:{}:{}", blockchain.as_str(), new_index)
                    )
                ]);
            }

            // Add CRUD buttons
            keyboard_rows.push(vec![
                teloxide::types::InlineKeyboardButton::callback(
                    "🔄 Refresh".to_string(),
                    format!("view_active_snipes_{}", blockchain.as_str())
                ),
                teloxide::types::InlineKeyboardButton::callback(
                    "❌ Cancel Snipe".to_string(),
                    format!("cancel_snipe:{}:{}", blockchain.as_str(), snipe_id)
                )
            ]);

            // Add new snipe button
            keyboard_rows.push(vec![
                teloxide::types::InlineKeyboardButton::callback(
                    "🆕 New Snipe".to_string(),
                    format!("sniper_token_{}", blockchain.as_str())
                )
            ]);

            // Add back buttons
            keyboard_rows.push(vec![
                teloxide::types::InlineKeyboardButton::callback(
                    "◀️ Back to Sniper Dashboard".to_string(),
                    format!("sniper_dashboard_{}", blockchain.as_str())
                ),
                teloxide::types::InlineKeyboardButton::callback(
                    "🏠 Dashboard".to_string(),
                    format!("view_{}", blockchain.as_str())
                )
            ]);

            let keyboard = teloxide::types::InlineKeyboardMarkup::new(keyboard_rows);

            // Edit the message with the active snipes
            bot_service.edit_message_with_keyboard(chat_id, message_id, &text, keyboard).await?;
        }

        cmd if cmd.starts_with("edit_snipe:") => {
            // Parse the command to get the blockchain and token address
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() < 3 {
                return Err(BotError::user_error("Invalid command format".to_string()));
            }

            let blockchain_str = parts[1];
            let shortened_token = parts[2];

            // Get the full token address from the user's config
            let token_address = match blockchain_str {
                "bsc" => user_data.config.bsc_current_contract_address.clone(),
                "sol" => user_data.config.sol_current_contract_address.clone(),
                "eth" => user_data.config.eth_current_contract_address.clone(),
                "base" => user_data.config.base_current_contract_address.clone(),
                _ => None,
            }.unwrap_or_else(|| shortened_token.to_string());

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Answer the callback query FIRST to prevent timeout
            bot_service.answer_callback_query(callback_id, Some(&format!("Loading snipe config for {}", blockchain_str.to_uppercase())), false).await?;

            // Get the user data
            let tg_user = bot_service.bot().get_chat_member(
                teloxide::types::ChatId(chat_id),
                teloxide::types::UserId(chat_id as u64)
            ).await?.user;

            // Get user data
            let user_data = bot_service.user_service().get_or_create_user_data(chat_id, &tg_user).await?;

            // Show the snipe config screen
            crate::screens::snipe_config_screen::show_snipe_config_screen(
                bot_service,
                &user_data,
                message_id,
                blockchain,
                &token_address
            ).await?;
        }

        cmd if cmd.starts_with("cancel_snipe:") => {
            // Parse the command to get the blockchain and snipe ID
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() < 3 {
                return Err(BotError::user_error("Invalid command format".to_string()));
            }

            let blockchain_str = parts[1];
            let snipe_id_str = parts[2];

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Answer the callback query FIRST to prevent timeout
            bot_service.answer_callback_query(callback_id, Some("Cancelling snipe..."), false).await?;

            // Convert the snipe ID string to ObjectId
            let snipe_id = match mongodb::bson::oid::ObjectId::parse_str(snipe_id_str) {
                Ok(id) => id,
                Err(_) => return Err(BotError::user_error("Invalid snipe ID".to_string())),
            };

            // Remove the snipe from the user's snipes
            crate::service::DbService::remove_snipe_from_user(chat_id, &blockchain, snipe_id).await?;

            // Show success message
            let success_text = format!(
                "✅ <b>Snipe Cancelled Successfully</b>\n\n\
                The snipe has been removed from your active snipes."
            );

            // Create keyboard with back buttons
            let keyboard = teloxide::types::InlineKeyboardMarkup::new(vec![
                vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "👁️ View Active Snipes".to_string(),
                        format!("view_active_snipes_{}", blockchain.as_str())
                    )
                ],
                vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "◀️ Back to Sniper Dashboard".to_string(),
                        format!("sniper_dashboard_{}", blockchain.as_str())
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "🏠 Dashboard".to_string(),
                        format!("view_{}", blockchain.as_str())
                    )
                ]
            ]);

            // Edit the message with the success text
            bot_service.edit_message_with_keyboard(chat_id, message_id, &success_text, keyboard).await?;
        }

        cmd if cmd.starts_with("view_active_snipes_") => {
            // Get the blockchain from the command
            let blockchain_str = cmd.trim_start_matches("view_active_snipes_");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Answer the callback query FIRST to prevent timeout
            bot_service.answer_callback_query(callback_id, Some(&format!("Loading active {} snipes", blockchain_str.to_uppercase())), false).await?;

            // Get the user data
            let tg_user = bot_service.bot().get_chat_member(
                teloxide::types::ChatId(chat_id),
                teloxide::types::UserId(chat_id as u64)
            ).await?.user;

            // Get user data
            let user_data = bot_service.user_service().get_or_create_user_data(chat_id, &tg_user).await?;

            // Fetch active snipes from the database
            let snipes = crate::service::DbService::get_user_snipes_by_blockchain(chat_id, &blockchain).await?;

            let active_snipes_text = if snipes.is_empty() {
                format!(
                    "👁️ <b>Active {} Snipes</b>\n\n\
                    You don't have any active snipes on {} blockchain yet.\n\n\
                    Use the 'New Snipe' button to set up a snipe for a token.",
                    blockchain.to_string().to_uppercase(),
                    blockchain.to_string().to_uppercase()
                )
            } else {
                // Format the snipes
                let mut text = format!(
                    "👁️ <b>Active {} Snipes</b>\n\n\
                    You have {} active snipe(s) on {} blockchain:\n\n",
                    blockchain.to_string().to_uppercase(),
                    snipes.len(),
                    blockchain.to_string().to_uppercase()
                );

                // Show the first snipe
                let snipe = &snipes[0];
                let status_str = match snipe.status {
                    crate::model::SnipeStatus::Pending => "⏳ Pending",
                    crate::model::SnipeStatus::Executed => "✅ Executed",
                    crate::model::SnipeStatus::Completed => "✅ Completed",
                    crate::model::SnipeStatus::Failed => "❌ Failed",
                    crate::model::SnipeStatus::Cancelled => "🚫 Cancelled",
                };

                text.push_str(&format!(
                    "<b>Snipe #{}/{}</b>\n\
                    Token: <code>{}</code>\n\
                    Amount: {} {}\n\
                    Slippage: {}%\n\
                    Anti-Rug: {}\n\
                    Anti-MEV: {}\n\
                    Status: {}\n\
                    Created: {}\n",
                    1,
                    snipes.len(),
                    snipe.contract_address,
                    snipe.amount.to_string(),
                    blockchain.get_native_symbol(),
                    snipe.slippage.to_string(),
                    if snipe.antirug { "✅ Enabled" } else { "❌ Disabled" },
                    if snipe.anti_mev { "✅ Enabled" } else { "❌ Disabled" },
                    status_str,
                    snipe.created_at.format("%Y-%m-%d %H:%M:%S")
                ));

                text
            };

            // Create keyboard with navigation and CRUD buttons
            let mut keyboard_rows = Vec::new();

            // Add navigation buttons if there are snipes
            if !snipes.is_empty() {
                let snipe = &snipes[0];
                let snipe_id = snipe.id.unwrap().to_hex();

                // Add navigation buttons if there are multiple snipes
                if snipes.len() > 1 {
                    keyboard_rows.push(vec![
                        teloxide::types::InlineKeyboardButton::callback(
                            "⬅️ Previous".to_string(),
                            format!("snipe_prev:{}:{}", blockchain.as_str(), 0) // Current index is 0
                        ),
                        teloxide::types::InlineKeyboardButton::callback(
                            "➡️ Next".to_string(),
                            format!("snipe_next:{}:{}", blockchain.as_str(), 0) // Current index is 0
                        )
                    ]);
                }

                // Add CRUD buttons
                keyboard_rows.push(vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "✏️ Edit Snipe".to_string(),
                        format!("edit_snipe:{}:{}", blockchain.as_str(), snipe.contract_address.clone())
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "❌ Cancel Snipe".to_string(),
                        format!("cancel_snipe:{}:{}", blockchain.as_str(), snipe_id)
                    )
                ]);

                // Add refresh button
                keyboard_rows.push(vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "🔄 Refresh".to_string(),
                        format!("view_active_snipes_{}", blockchain.as_str())
                    )
                ]);
            }

            // Add new snipe button
            keyboard_rows.push(vec![
                teloxide::types::InlineKeyboardButton::callback(
                    "🆕 New Snipe".to_string(),
                    format!("sniper_token_{}", blockchain.as_str())
                )
            ]);

            // Add back buttons
            keyboard_rows.push(vec![
                teloxide::types::InlineKeyboardButton::callback(
                    "◀️ Back to Sniper Dashboard".to_string(),
                    format!("sniper_dashboard_{}", blockchain.as_str())
                ),
                teloxide::types::InlineKeyboardButton::callback(
                    "🏠 Dashboard".to_string(),
                    format!("view_{}", blockchain.as_str())
                )
            ]);

            let keyboard = teloxide::types::InlineKeyboardMarkup::new(keyboard_rows);

            // Edit the message with the active snipes
            bot_service.edit_message_with_keyboard(chat_id, message_id, &active_snipes_text, keyboard).await?;
        }
        "cleanup_chat" => {
            // Clean up the chat
            bot_service.cleanup_conversation(chat_id).await?;
            bot_service.answer_callback_query(callback_id, Some("Chat cleaned up"), false).await?;
        }
        "refresh_wallet" => {
            // Refresh wallet balances
            let updated_user_data = bot_service.user_service().refresh_user_wallets(chat_id).await?;

            // Update the dashboard with refreshed data
            show_dashboard(bot_service, &updated_user_data, message_id).await?;

            // Show a brief notification
            bot_service.answer_callback_query(callback_id, Some("Wallet balances refreshed"), false).await?;
        }
        cmd if cmd.starts_with("generate_new_") && cmd.ends_with("_wallet") => {
            // Extract the blockchain from the command
            let parts: Vec<&str> = cmd.split('_').collect();
            if parts.len() >= 4 {
                let blockchain_str = parts[2];

                let blockchain = match blockchain_str {
                    "bsc" => Blockchain::BSC,
                    "sol" => Blockchain::SOL,
                    "eth" => Blockchain::ETH,
                    "base" => Blockchain::BASE,
                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                };

                // Show confirmation message
                let confirm_text = format!(
                    "⚠️ <b>Warning: Generate New Wallet</b>\n\n\
                    You are about to generate a new {} wallet.\n\n\
                    <b>Your current wallet and all its funds will no longer be accessible through this bot.</b>\n\n\
                    Are you sure you want to continue?",
                    blockchain.as_str().to_uppercase()
                );

                // Create confirmation keyboard
                let keyboard = InlineKeyboardMarkup::new(vec![
                    vec![
                        InlineKeyboardButton::callback(
                            "✅ Yes, Generate New Wallet".to_string(),
                            format!("confirm_new_wallet:{}", blockchain.as_str()),
                        ),
                    ],
                    vec![
                        InlineKeyboardButton::callback(
                            "❌ No, Keep Current Wallet".to_string(),
                            format!("view_{}", blockchain.as_str()),
                        ),
                    ],
                ]);

                // Edit the message to show the confirmation
                bot_service.edit_message_with_keyboard(chat_id, message_id, &confirm_text, keyboard).await?;

                // Answer the callback query
                bot_service.answer_callback_query(callback_id, None, false).await?;
            } else {
                bot_service.answer_callback_query(callback_id, Some("Invalid command format"), false).await?;
            }
        }
        cmd if cmd.starts_with("confirm_new_wallet:") => {
            // Extract the blockchain from the command
            let blockchain_str = cmd.trim_start_matches("confirm_new_wallet:");

            let blockchain = match blockchain_str {
                "bsc" => Blockchain::BSC,
                "sol" => Blockchain::SOL,
                "eth" => Blockchain::ETH,
                "base" => Blockchain::BASE,
                _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
            };

            // Show generating message
            let generating_text = format!(
                "🔄 <b>Generating New {} Wallet</b>\n\n\
                Please wait while we generate your new wallet...",
                blockchain.as_str().to_uppercase()
            );

            // Edit the message to show generating status
            bot_service.edit_message(chat_id, message_id, &generating_text).await?;

            // Answer the callback query
            bot_service.answer_callback_query(callback_id, None, false).await?;

            // Generate new wallet for the specific blockchain
            let new_wallet = bot_service.blockchain_service().generate_wallet(blockchain.clone()).await?;

            // Update the user's wallet in the database
            let updated_user_data = bot_service.user_service().update_user_wallet(chat_id, blockchain.clone(), new_wallet).await?;

            // Show the dashboard with the new wallet
            show_dashboard(bot_service, &updated_user_data, message_id).await?;

            // Show success notification
            bot_service.answer_callback_query(callback_id, Some(&format!("New {} wallet generated successfully", blockchain.as_str().to_uppercase())), false).await?;
        }
        // 🚀 NEW: Send new message for token refresh (don't edit existing receipts)
        cmd if cmd.starts_with("new_msg_refresh_token:") => {
            let parts: Vec<&str> = cmd.split(':').collect();
            if parts.len() >= 3 {
                let blockchain_str = parts[1];
                let token_address = parts[2];

                let blockchain = match blockchain_str {
                    "bsc" => Blockchain::BSC,
                    "sol" => Blockchain::SOL,
                    "eth" => Blockchain::ETH,
                    "base" => Blockchain::BASE,
                    _ => return Err(BotError::user_error(format!("Unknown blockchain: {}", blockchain_str))),
                };

                // Send new message instead of editing existing one
                handle_refresh_token_new_message(bot_service, callback_id, chat_id, token_address, &blockchain).await?;
                return Ok(());
            }
        }
        _ => {
            // Handle unknown callback
            println!("Unknown string command: {}", command);
            bot_service.answer_callback_query(callback_id, Some("Unknown action"), false).await?;
        }
    }

    Ok(())
}
