[2025-08-10T10:17:43Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-08-10T10:17:43Z INFO  Easybot] Starting EasyBot...
[2025-08-10T10:17:43Z INFO  Easybot] Environment variables for port configuration:
[2025-08-10T10:17:43Z INFO  Easybot]   PORT=8000
[2025-08-10T10:17:43Z INFO  Easybot]   SOCKET_PORT=3000
[2025-08-10T10:17:43Z INFO  Easybot] Using port 8000 from config for health check service
[2025-08-10T10:17:43Z INFO  Easybot] Using port 3000 from config for socket service
[2025-08-10T10:17:43Z INFO  Easybot] Initializing database connection...
[2025-08-10T10:17:43Z INFO  Easybot::service::db_service] Initializing database connection...
