[2025-08-10T10:17:43Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-08-10T10:17:43Z INFO  Easybot] Starting EasyBot...
[2025-08-10T10:17:43Z INFO  Easybot] Environment variables for port configuration:
[2025-08-10T10:17:43Z INFO  Easybot]   PORT=8000
[2025-08-10T10:17:43Z INFO  Easybot]   SOCKET_PORT=3000
[2025-08-10T10:17:43Z INFO  Easybot] Using port 8000 from config for health check service
[2025-08-10T10:17:43Z INFO  Easybot] Using port 3000 from config for socket service
[2025-08-10T10:17:43Z INFO  Easybot] Initializing database connection...
[2025-08-10T10:17:43Z INFO  Easybot::service::db_service] Initializing database connection...
[2025-08-10T10:17:58Z INFO  Easybot::service::db_service] Creating indexes...
[2025-08-10T10:18:04Z INFO  Easybot::service::db_service] Index creation took: 5.9752407s
[2025-08-10T10:18:04Z INFO  Easybot::service::db_service] Database initialization complete in: 20.285005s
[2025-08-10T10:18:04Z INFO  Easybot] Database connection established
[2025-08-10T10:18:04Z INFO  Easybot] Starting health check service on port 8000...
[2025-08-10T10:18:04Z INFO  Easybot] Initializing admin service and controllers...
[2025-08-10T10:18:04Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-08-10T10:18:04Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-08-10T10:18:04Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-08-10T10:18:04Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-08-10T10:18:04Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-08-10T10:18:05Z INFO  Easybot] Health check and admin services initialization completed
[2025-08-10T10:18:05Z INFO  Easybot] Checking and initializing default super admin...
[2025-08-10T10:18:05Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-08-10T10:18:06Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-08-10T10:18:06Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-08-10T10:18:06Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-08-10T10:18:06Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-08-10T10:18:06Z INFO  Easybot] Initializing cached price service...
[2025-08-10T10:18:06Z INFO  Easybot::service::cached_price_service] Initializing price cache for all supported blockchains...
[2025-08-10T10:18:06Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-08-10T10:18:07Z INFO  Easybot::service::price_service] DexScreener price for SOL: $179.94000000
[2025-08-10T10:18:07Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $179.9400
[2025-08-10T10:18:07Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for SOL: $179.9400
[2025-08-10T10:18:08Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-08-10T10:18:09Z INFO  Easybot::service::price_service] DexScreener price for ETH: $4217.49000000
[2025-08-10T10:18:09Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $4217.4900
[2025-08-10T10:18:09Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for ETH: $4217.4900
[2025-08-10T10:18:10Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BSC with rate limiting...
[2025-08-10T10:18:11Z INFO  Easybot::service::price_service] DexScreener price for BSC: $802.61000000
[2025-08-10T10:18:11Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $802.6100
[2025-08-10T10:18:11Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BSC: $802.6100
[2025-08-10T10:18:12Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BASE with rate limiting...
[2025-08-10T10:18:13Z INFO  Easybot::service::price_service] DexScreener price for BASE: $4216.13000000
[2025-08-10T10:18:13Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $4216.1300
[2025-08-10T10:18:13Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BASE: $4216.1300
[2025-08-10T10:18:13Z INFO  Easybot::service::cached_price_service] Price cache initialization completed
[2025-08-10T10:18:13Z INFO  Easybot] Price cache initialized successfully
[2025-08-10T10:18:13Z INFO  Easybot] Starting background price updater...
[2025-08-10T10:18:13Z INFO  Easybot::service::cached_price_service] Starting background price updater (interval: 1800s)
[2025-08-10T10:18:13Z INFO  Easybot] Background price updater started
[2025-08-10T10:18:13Z INFO  Easybot::service::cached_price_service] 🔄 Background price update started
[2025-08-10T10:18:13Z INFO  Easybot] Initializing Solana trader service...
[2025-08-10T10:18:14Z INFO  Easybot] Solana trader service initialized
[2025-08-10T10:18:14Z INFO  Easybot] Initializing snipe worker...
[2025-08-10T10:18:14Z INFO  Easybot] Snipe worker initialized and started
[2025-08-10T10:18:14Z INFO  Easybot] Starting background fee collection service...
[2025-08-10T10:18:14Z INFO  Easybot] Background fee collection service started
[2025-08-10T10:18:14Z INFO  Easybot] Starting bot dispatcher...
[2025-08-10T10:18:14Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-08-10T10:18:14Z INFO  Easybot::service::price_service] Using cached native price for SOL: $179.9400
[2025-08-10T10:18:14Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $179.9400
[2025-08-10T10:18:15Z INFO  Easybot] Received callback query with data: {"command":"view_sol"}
[2025-08-10T10:18:19Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-08-10T10:18:19Z INFO  Easybot::service::price_service] Using cached native price for ETH: $4217.4900
[2025-08-10T10:18:19Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $4217.4900
[2025-08-10T10:18:21Z INFO  Easybot] Received callback query with data: dismiss_message
[2025-08-10T10:18:25Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BSC with rate limiting...
[2025-08-10T10:18:25Z INFO  Easybot::service::price_service] Using cached native price for BSC: $802.6100
[2025-08-10T10:18:25Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $802.6100
[2025-08-10T10:18:29Z INFO  Easybot] Received callback query with data: scan_sol
[2025-08-10T10:18:30Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BASE with rate limiting...
[2025-08-10T10:18:30Z INFO  Easybot::service::price_service] Using cached native price for BASE: $4216.1300
[2025-08-10T10:18:30Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $4216.1300
[2025-08-10T10:18:35Z INFO  Easybot::service::cached_price_service] 🔄 Background price update completed
[2025-08-10T10:20:21Z INFO  Easybot] Received callback query with data: dashboard_sol_settings
[2025-08-10T10:20:30Z INFO  Easybot] Received callback query with data: dashboard_sol_overview
[2025-08-10T10:20:41Z INFO  Easybot] Received callback query with data: dashboard_sol_rewards
[2025-08-10T10:21:54Z INFO  Easybot] Received callback query with data: dashboard_sol_overview
[2025-08-10T10:21:58Z INFO  Easybot] Received callback query with data: refresh_sol
[2025-08-10T10:23:21Z INFO  Easybot] Received callback query with data: dashboard_sol_rewards
[2025-08-10T10:23:28Z INFO  Easybot] Received callback query with data: dashboard_sol_trading
[2025-08-10T10:24:04Z INFO  Easybot] Received callback query with data: sniper_sol
[2025-08-10T10:24:20Z INFO  Easybot] Received callback query with data: dashboard_sol_rewards
[2025-08-10T10:25:21Z INFO  Easybot] Received callback query with data: dashboard_sol_settings
[2025-08-10T10:25:45Z INFO  Easybot] Received callback query with data: dashboard_sol_overview
[2025-08-10T10:25:59Z INFO  Easybot] Received callback query with data: dashboard_sol_wallets
[2025-08-10T10:26:04Z INFO  Easybot] Received callback query with data: dashboard_sol_overview
[2025-08-10T10:40:57Z INFO  teloxide::dispatching::dispatcher] ^C received, trying to shutdown the dispatcher...
[2025-08-10T10:40:57Z INFO  teloxide::utils::shutdown_token] Trying to shutdown the dispatcher...
