use std::sync::Arc;
use tracing::{info, warn, error, debug};
use teloxide::types::InlineKeyboardMarkup;
use crate::model::{
    BotError, UserData, Blockchain
};
use crate::service::{BotService, UserProfileService};

/// Different dashboard views available
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum DashboardView {
    Overview,
    Rewards,
    Settings,
    Trading,
    Wallets,
}

impl DashboardView {
    pub fn as_str(&self) -> &'static str {
        match self {
            DashboardView::Overview => "overview",
            DashboardView::Rewards => "rewards",
            DashboardView::Settings => "settings", 
            DashboardView::Trading => "trading",
            DashboardView::Wallets => "wallets",
        }
    }

    pub fn display_name(&self) -> &'static str {
        match self {
            DashboardView::Overview => "📊 Overview",
            DashboardView::Rewards => "🎁 Rewards",
            DashboardView::Settings => "⚙️ Settings",
            DashboardView::Trading => "💱 Trading",
            DashboardView::Wallets => "👛 Wallets",
        }
    }
}

/// Unified dashboard service that works across all blockchains
#[derive(Debug, Clone)]
pub struct UnifiedDashboardService {
    profile_service: Arc<UserProfileService>,
}

impl UnifiedDashboardService {
    pub fn new() -> Self {
        Self {
            profile_service: Arc::new(UserProfileService::new()),
        }
    }

    /// Show unified dashboard for any blockchain with specified view
    pub async fn show_dashboard(
        &self,
        bot_service: &BotService,
        user_data: &UserData,
        message_id: i32,
        view: DashboardView,
    ) -> Result<(), BotError> {
        let blockchain = user_data.current_blockchain();

        debug!("Showing {} dashboard for {} with view: {:?}",
               blockchain.as_str(), user_data.display_name(), view);

        // Update user session with current dashboard state
        let mut updated_user_data = user_data.clone();
        updated_user_data.session.set_dashboard_state(message_id, view.as_str());
        bot_service.user_service().cache_user_data(updated_user_data).await;

        // Get the dashboard content based on view
        let (dashboard_text, keyboard) = match view {
            DashboardView::Overview => self.create_overview_content(user_data, blockchain).await?,
            DashboardView::Rewards => self.create_rewards_content(user_data, blockchain).await?,
            DashboardView::Settings => self.create_settings_content(user_data, blockchain).await?,
            DashboardView::Trading => self.create_trading_content(user_data, blockchain).await?,
            DashboardView::Wallets => self.create_wallets_content(user_data, blockchain).await?,
        };

        // Update the message with new content
        bot_service.edit_message_with_keyboard(
            user_data.chat_id(),
            message_id,
            &dashboard_text,
            keyboard,
        ).await?;

        Ok(())
    }

    /// Get the current dashboard view from user session
    pub fn get_current_view(user_data: &UserData) -> DashboardView {
        if let Some(view_str) = user_data.session.get_dashboard_view() {
            match view_str {
                "rewards" => DashboardView::Rewards,
                "settings" => DashboardView::Settings,
                "trading" => DashboardView::Trading,
                "wallets" => DashboardView::Wallets,
                _ => DashboardView::Overview,
            }
        } else {
            DashboardView::Overview
        }
    }

    /// Check if user has an active dashboard message
    pub fn has_active_dashboard(user_data: &UserData) -> bool {
        user_data.session.dashboard_message_id.is_some()
    }

    /// Get the active dashboard message ID if available
    pub fn get_active_dashboard_message_id(user_data: &UserData) -> Option<i32> {
        user_data.session.dashboard_message_id
    }

    /// Create overview content (main dashboard view)
    async fn create_overview_content(
        &self,
        user_data: &UserData,
        blockchain: Blockchain,
    ) -> Result<(String, InlineKeyboardMarkup), BotError> {
        let mut text = String::with_capacity(1024);
        
        // Header
        text.push_str(&format!("📊 <b>{} Dashboard</b>\n\n", blockchain.as_str().to_uppercase()));
        
        // User info
        text.push_str(&format!("👤 <b>User:</b> {}\n\n", user_data.display_name()));
        
        // Wallet address
        let wallet = user_data.get_wallet(&blockchain);
        text.push_str(&format!("📍 <b>Address:</b> <code>{}</code>\n", wallet.address));
        
        // Explorer link
        let explorer_url = crate::constants::explorer_urls::get_explorer_address_url(
            blockchain.as_str(), &wallet.address
        );
        let explorer_name = match blockchain {
            Blockchain::BSC => "BSCScan",
            Blockchain::SOL => "Solscan", 
            Blockchain::ETH => "Etherscan",
            Blockchain::BASE => "BaseScan",
        };
        text.push_str(&format!("🔍 <a href=\"{}\">View on {}</a>\n\n", explorer_url, explorer_name));
        
        // Blockchain info
        let blockchain_name = match blockchain {
            Blockchain::BSC => "BSC (Binance Smart Chain)",
            Blockchain::SOL => "Solana",
            Blockchain::ETH => "Ethereum",
            Blockchain::BASE => "Base",
        };
        text.push_str(&format!("🔗 <b>Blockchain:</b> {}\n\n", blockchain_name));
        
        // Balance (placeholder - would be loaded asynchronously)
        text.push_str("💰 <b>Balance:</b> <code>Loading...</code>\n\n");
        
        // Compact rewards summary
        if let Some(user_id) = user_data.user.id {
            if let Ok(rewards_summary) = self.profile_service.format_compact_rewards_summary(user_id).await {
                text.push_str(&format!("{}\n\n", rewards_summary));
            }
        }
        
        // Network status
        text.push_str("📈 <b>Network Status:</b> Active\n\n");
        
        // Quick settings summary
        let config = user_data.config.get_blockchain_config(&blockchain);
        text.push_str(&format!(
            "⚙️ <b>Quick Settings:</b> Slippage: {}%, AntiRug: {}, AutoBuy: {}",
            config.slippage,
            if config.antirug { "✅" } else { "❌" },
            if config.auto_buy { "✅" } else { "❌" }
        ));

        let keyboard = self.create_dashboard_keyboard(blockchain, DashboardView::Overview);
        Ok((text, keyboard))
    }

    /// Create rewards content
    async fn create_rewards_content(
        &self,
        user_data: &UserData,
        blockchain: Blockchain,
    ) -> Result<(String, InlineKeyboardMarkup), BotError> {
        let mut text = String::with_capacity(1024);
        
        // Header
        text.push_str(&format!("🎁 <b>{} Dashboard - Rewards</b>\n\n", blockchain.as_str().to_uppercase()));
        
        // Get detailed rewards information
        if let Some(user_id) = user_data.user.id {
            match self.profile_service.get_user_rewards(user_id).await {
                Ok(rewards) => {
                    let reward_rate = if rewards.total_trading_volume_usd > 0.0 {
                        rewards.total_tokens_earned / rewards.total_trading_volume_usd
                    } else {
                        0.0
                    };

                    text.push_str(&format!(
                        "💰 <b>Total Tokens Earned:</b> {:.2}\n\
                        📊 <b>Total Trading Volume:</b> ${:.2}\n\
                        🏆 <b>Average Reward Rate:</b> {:.2} tokens per $1\n\n",
                        rewards.total_tokens_earned,
                        rewards.total_trading_volume_usd,
                        reward_rate
                    ));

                    // Blockchain-specific breakdown
                    text.push_str("📈 <b>Breakdown by Blockchain:</b>\n");
                    
                    let (current_volume, current_tokens) = match blockchain {
                        Blockchain::BSC => (rewards.volume_breakdown.bsc_volume_usd, rewards.reward_breakdown.bsc_tokens_earned),
                        Blockchain::SOL => (rewards.volume_breakdown.sol_volume_usd, rewards.reward_breakdown.sol_tokens_earned),
                        Blockchain::ETH => (rewards.volume_breakdown.eth_volume_usd, rewards.reward_breakdown.eth_tokens_earned),
                        Blockchain::BASE => (rewards.volume_breakdown.base_volume_usd, rewards.reward_breakdown.base_tokens_earned),
                    };

                    text.push_str(&format!(
                        "🔸 <b>{}:</b> {:.2} tokens (${:.2} volume)\n",
                        blockchain.as_str().to_uppercase(),
                        current_tokens,
                        current_volume
                    ));

                    // Show other blockchains
                    if rewards.volume_breakdown.bsc_volume_usd > 0.0 && blockchain != Blockchain::BSC {
                        text.push_str(&format!("🔸 <b>BSC:</b> {:.2} tokens (${:.2} volume)\n",
                            rewards.reward_breakdown.bsc_tokens_earned, rewards.volume_breakdown.bsc_volume_usd));
                    }
                    if rewards.volume_breakdown.sol_volume_usd > 0.0 && blockchain != Blockchain::SOL {
                        text.push_str(&format!("🔸 <b>SOL:</b> {:.2} tokens (${:.2} volume)\n",
                            rewards.reward_breakdown.sol_tokens_earned, rewards.volume_breakdown.sol_volume_usd));
                    }
                    if rewards.volume_breakdown.eth_volume_usd > 0.0 && blockchain != Blockchain::ETH {
                        text.push_str(&format!("🔸 <b>ETH:</b> {:.2} tokens (${:.2} volume)\n",
                            rewards.reward_breakdown.eth_tokens_earned, rewards.volume_breakdown.eth_volume_usd));
                    }
                    if rewards.volume_breakdown.base_volume_usd > 0.0 && blockchain != Blockchain::BASE {
                        text.push_str(&format!("🔸 <b>BASE:</b> {:.2} tokens (${:.2} volume)\n",
                            rewards.reward_breakdown.base_tokens_earned, rewards.volume_breakdown.base_volume_usd));
                    }
                }
                Err(_) => {
                    text.push_str("❌ <b>Unable to load rewards data</b>\n\nPlease try again later or contact support if the issue persists.");
                }
            }
        } else {
            text.push_str("❌ <b>User ID not found</b>\n\nUnable to load rewards data.");
        }

        text.push_str("\n\n<i>Rewards are automatically calculated when you trade!</i>");

        let keyboard = self.create_dashboard_keyboard(blockchain, DashboardView::Rewards);
        Ok((text, keyboard))
    }

    /// Create settings content
    async fn create_settings_content(
        &self,
        user_data: &UserData,
        blockchain: Blockchain,
    ) -> Result<(String, InlineKeyboardMarkup), BotError> {
        let mut text = String::with_capacity(1024);
        
        // Header
        text.push_str(&format!("⚙️ <b>{} Dashboard - Settings</b>\n\n", blockchain.as_str().to_uppercase()));
        
        let config = user_data.config.get_blockchain_config(&blockchain);
        
        text.push_str(&format!(
            "<b>Trading Settings:</b>\n\
            • <b>Slippage:</b> {}%\n\
            • <b>Anti-Rug Protection:</b> {}\n\
            • <b>Anti-MEV Protection:</b> {}\n\
            • <b>Max Gas Price:</b> {} Gwei\n\
            • <b>Max Gas Limit:</b> {}\n\n\
            <b>Auto-Trading:</b>\n\
            • <b>Auto-Buy:</b> {}\n\
            • <b>Min Liquidity:</b> ${}\n\
            • <b>Max Market Cap:</b> ${}\n\
            • <b>Max Liquidity:</b> ${}\n\
            • <b>Auto-Sell:</b> {}\n\
            • <b>Sell High:</b> {}%\n\
            • <b>Sell Low:</b> {}%\n\
            • <b>Auto-Approve:</b> {}\n\n\
            <i>Tap a setting to change it</i>",
            config.slippage,
            if config.antirug { "✅ Enabled" } else { "❌ Disabled" },
            if config.anti_mev { "✅ Enabled" } else { "❌ Disabled" },
            config.max_gas_price,
            config.max_gas_limit,
            if config.auto_buy { "✅ Enabled" } else { "❌ Disabled" },
            config.min_liquidity,
            config.max_market_cap,
            config.max_liquidity,
            if config.auto_sell { "✅ Enabled" } else { "❌ Disabled" },
            config.sell_high,
            config.sell_low,
            if config.auto_approve { "✅ Enabled" } else { "❌ Disabled" }
        ));

        let keyboard = self.create_dashboard_keyboard(blockchain, DashboardView::Settings);
        Ok((text, keyboard))
    }

    /// Create trading content
    async fn create_trading_content(
        &self,
        user_data: &UserData,
        blockchain: Blockchain,
    ) -> Result<(String, InlineKeyboardMarkup), BotError> {
        let mut text = String::with_capacity(1024);
        
        // Header
        text.push_str(&format!("💱 <b>{} Dashboard - Trading</b>\n\n", blockchain.as_str().to_uppercase()));
        
        text.push_str(&format!(
            "🔍 <b>Scan Contract</b>\n\
            Analyze any token contract for safety and trading opportunities\n\n\
            💰 <b>Quick Buy/Sell</b>\n\
            Fast trading with your configured settings\n\n\
            📊 <b>Active Trades</b>\n\
            View and manage your current positions\n\n\
            🌐 <b>Multi-Chain Trading</b>\n\
            Switch between SOL, BSC, ETH, and BASE using the blockchain buttons below\n\n\
            ⚡ <b>Current Blockchain:</b> {}\n\
            💳 <b>Wallet:</b> <code>{}</code>\n\n\
            <i>Use the action buttons to start trading on any blockchain</i>",
            blockchain.as_str().to_uppercase(),
            user_data.get_wallet(&blockchain).address
        ));

        let keyboard = self.create_dashboard_keyboard(blockchain, DashboardView::Trading);
        Ok((text, keyboard))
    }

    /// Create wallets content
    async fn create_wallets_content(
        &self,
        user_data: &UserData,
        blockchain: Blockchain,
    ) -> Result<(String, InlineKeyboardMarkup), BotError> {
        let mut text = String::with_capacity(1024);
        
        // Header
        text.push_str(&format!("👛 <b>{} Dashboard - Wallets</b>\n\n", blockchain.as_str().to_uppercase()));
        
        text.push_str("🔐 <b>Your Wallet Addresses:</b>\n\n");
        
        // Show all wallet addresses
        text.push_str(&format!(
            "🔸 <b>BSC:</b> <code>{}</code>\n\
            🔸 <b>SOL:</b> <code>{}</code>\n\
            🔸 <b>ETH:</b> <code>{}</code>\n\
            🔸 <b>BASE:</b> <code>{}</code>\n\n",
            user_data.wallets.bsc_wallet.address,
            user_data.wallets.sol_wallet.address,
            user_data.wallets.eth_wallet.address,
            user_data.wallets.base_wallet.address
        ));

        // Current blockchain highlight
        let current_wallet = user_data.get_wallet(&blockchain);
        text.push_str(&format!(
            "⭐ <b>Current ({}):</b>\n\
            📍 <b>Address:</b> <code>{}</code>\n\n\
            🔑 <b>Private Key Management:</b>\n\
            Use /privatekeys to view your private keys\n\
            (Messages auto-delete after 60 seconds)\n\n\
            🔄 <b>Generate New Wallet:</b>\n\
            Create a fresh wallet for this blockchain\n\n\
            <i>⚠️ Always keep your private keys secure!</i>",
            blockchain.as_str().to_uppercase(),
            current_wallet.address
        ));

        let keyboard = self.create_dashboard_keyboard(blockchain, DashboardView::Wallets);
        Ok((text, keyboard))
    }

    /// Create unified dashboard keyboard with sub-menu navigation
    fn create_dashboard_keyboard(&self, blockchain: Blockchain, current_view: DashboardView) -> InlineKeyboardMarkup {
        let mut keyboard_rows = vec![];

        // Sub-menu navigation row (top row)
        let mut submenu_row = vec![];
        
        for view in [DashboardView::Overview, DashboardView::Rewards, DashboardView::Settings, DashboardView::Trading, DashboardView::Wallets] {
            let button_text = if view == current_view {
                format!("• {} •", view.display_name())
            } else {
                view.display_name().to_string()
            };
            
            submenu_row.push(teloxide::types::InlineKeyboardButton::callback(
                button_text,
                format!("dashboard_{}_{}", blockchain.as_str(), view.as_str())
            ));
        }
        
        // Split submenu into two rows if needed
        if submenu_row.len() > 3 {
            keyboard_rows.push(submenu_row[0..3].to_vec());
            keyboard_rows.push(submenu_row[3..].to_vec());
        } else {
            keyboard_rows.push(submenu_row);
        }

        // Action buttons based on current view
        match current_view {
            DashboardView::Overview => {
                keyboard_rows.push(vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "🔍 Scan Contract".to_string(),
                        format!("scan_{}", blockchain.as_str())
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "🔄 Refresh Wallet".to_string(),
                        format!("refresh_{}", blockchain.as_str())
                    ),
                ]);
            }
            DashboardView::Trading => {
                keyboard_rows.push(vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "🔍 Scan Contract".to_string(),
                        format!("scan_{}", blockchain.as_str())
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "📊 View Trades".to_string(),
                        format!("show_trades_{}", blockchain.as_str())
                    ),
                ]);
            }
            DashboardView::Wallets => {
                keyboard_rows.push(vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "🔑 Generate New Wallet".to_string(),
                        format!("generate_wallet_{}", blockchain.as_str())
                    ),
                ]);
            }
            DashboardView::Settings => {
                keyboard_rows.push(vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "⚙️ Configure Settings".to_string(),
                        format!("show_config_{}", blockchain.as_str())
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "🔄 Reset to Defaults".to_string(),
                        format!("reset_config_{}", blockchain.as_str())
                    ),
                ]);
            }
            _ => {}
        }

        // Blockchain switching row - show all blockchains except current one
        let mut blockchain_row = vec![];
        for bc in [Blockchain::SOL, Blockchain::BSC, Blockchain::ETH, Blockchain::BASE] {
            if bc != blockchain {
                blockchain_row.push(teloxide::types::InlineKeyboardButton::callback(
                    bc.as_str().to_uppercase(),
                    format!("switch_{}", bc.as_str())
                ));
            }
        }
        if !blockchain_row.is_empty() {
            keyboard_rows.push(blockchain_row);
        }

        // Bottom navigation row
        keyboard_rows.push(vec![
            teloxide::types::InlineKeyboardButton::callback(
                "🏠 Home".to_string(),
                "home"
            ),
            teloxide::types::InlineKeyboardButton::callback(
                "🧹 Clean chat".to_string(),
                "cleanup_chat"
            ),
            teloxide::types::InlineKeyboardButton::callback(
                "❌ Dismiss".to_string(),
                "dismiss_message"
            ),
        ]);

        InlineKeyboardMarkup::new(keyboard_rows)
    }
}
