use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::time::{Duration, Instant};
use teloxide::prelude::Requester;
use crate::model::{User, UserData, BotError, Blockchain};

#[derive(Debug)]
struct CacheEntry<T> {
    data: T,
    last_updated: Instant,
}

#[derive(Debug)]
pub struct UserService {
    user_data_cache: Arc<RwLock<HashMap<i64, CacheEntry<UserData>>>>,
    cache_expiration: Duration,
}

impl UserService {
    pub fn new() -> Self {
        Self {
            user_data_cache: Arc::new(RwLock::new(HashMap::new())),
            cache_expiration: Duration::from_secs(86400),
        }
    }

    pub fn with_expiration(cache_expiration_seconds: u64) -> Self {
        Self {
            user_data_cache: Arc::new(RwLock::new(HashMap::new())),
            cache_expiration: Duration::from_secs(cache_expiration_seconds),
        }
    }

    pub async fn get_user_data_from_cache(&self, chat_id: i64) -> Option<UserData> {
        let cache = self.user_data_cache.read().await;

        if let Some(entry) = cache.get(&chat_id) {
            if Instant::now().duration_since(entry.last_updated) < self.cache_expiration {
                return Some(entry.data.clone());
            }
        }

        None
    }

    pub async fn cache_user_data(&self, user_data: UserData) {
        let chat_id = user_data.chat_id();
        let mut cache = self.user_data_cache.write().await;

        cache.insert(chat_id, CacheEntry {
            data: user_data,
            last_updated: Instant::now(),
        });
    }

    pub async fn get_user(&self, chat_id: i64) -> Option<User> {
        if let Some(user_data) = self.get_user_data_from_cache(chat_id).await {
            return Some(user_data.user);
        }

        None
    }

    pub async fn get_or_create_user_data(&self, chat_id: i64, tg_user: &teloxide::types::User) -> Result<UserData, BotError> {
        if let Some(user_data) = self.get_user_data_from_cache(chat_id).await {
            println!("Cache hit for user_data: {}", user_data.display_name());
            return Ok(user_data);
        }

        println!("Cache miss for chat_id: {}, fetching from database", chat_id);

        let user_data = crate::service::DbService::get_or_create_user_data(chat_id, tg_user).await?;

        self.cache_user_data(user_data.clone()).await;

        Ok(user_data)
    }

    pub async fn update_user_blockchain(&self, chat_id: i64, blockchain: Blockchain) -> Result<UserData, BotError> {
        let bot = crate::service::BotService::get_instance().bot();
        let tg_user = match bot.get_chat_member(
            teloxide::types::ChatId(chat_id),
            teloxide::types::UserId(chat_id as u64)
        ).await {
            Ok(member) => member.user,
            Err(_) => return Err(BotError::user_error("Failed to get user information")),
        };

        let mut user_data = self.get_or_create_user_data(chat_id, &tg_user).await?;

        if user_data.current_blockchain() != blockchain {
            user_data.set_current_blockchain(blockchain);

            self.cache_user_data(user_data.clone()).await;

            crate::service::DbService::save_user(&user_data.user).await?;
        }

        Ok(user_data)
    }

    pub async fn cleanup_cache(&self) {
        let mut cache = self.user_data_cache.write().await;
        let now = Instant::now();

        cache.retain(|_, entry| now.duration_since(entry.last_updated) < self.cache_expiration);

        println!("Cache cleanup completed, remaining entries: {}", cache.len());
    }

    pub async fn get_cache_size(&self) -> usize {
        let cache = self.user_data_cache.read().await;
        cache.len()
    }

    pub async fn refresh_user_wallets(&self, chat_id: i64) -> Result<UserData, BotError> {
        let bot = crate::service::BotService::get_instance().bot();
        let tg_user = match bot.get_chat_member(
            teloxide::types::ChatId(chat_id),
            teloxide::types::UserId(chat_id as u64)
        ).await {
            Ok(member) => member.user,
            Err(_) => return Err(BotError::user_error("Failed to get user information")),
        };

        let user_data = self.get_or_create_user_data(chat_id, &tg_user).await?;

        println!("Refreshing wallet balances for user: {}", user_data.display_name());

        let mut updated_user_data = user_data.clone();
        updated_user_data.update_last_seen();

        // Force refresh wallet balances by clearing any cached balance data
        // This ensures we get fresh balance information from the blockchain
        let blockchain_service = crate::service::BotService::get_instance().blockchain_service();

        // Refresh balances for all blockchains
        for blockchain in [crate::model::Blockchain::SOL, crate::model::Blockchain::BSC,
                          crate::model::Blockchain::ETH, crate::model::Blockchain::BASE] {
            let wallet = updated_user_data.get_wallet(&blockchain);

            // Get fresh balance from blockchain
            match blockchain_service.get_balance(&wallet.address, blockchain).await {
                Ok(balance) => {
                    println!("Refreshed {} balance: {}", blockchain.as_str(), balance);
                    // Update the wallet balance in user data
                    // Note: We would need to add a method to update wallet balance in UserData
                    // For now, just log the refresh
                },
                Err(e) => {
                    println!("Failed to refresh {} balance: {}", blockchain.as_str(), e);
                }
            }
        }

        self.cache_user_data(updated_user_data.clone()).await;

        Ok(updated_user_data)
    }

    pub async fn update_user_wallet(&self, chat_id: i64, blockchain: Blockchain, new_wallet: crate::model::Wallet) -> Result<UserData, BotError> {
        let bot = crate::service::BotService::get_instance().bot();
        let tg_user = match bot.get_chat_member(
            teloxide::types::ChatId(chat_id),
            teloxide::types::UserId(chat_id as u64)
        ).await {
            Ok(member) => member.user,
            Err(_) => return Err(BotError::user_error("Failed to get user information")),
        };

        let mut user_data = self.get_or_create_user_data(chat_id, &tg_user).await?;

        match blockchain {
            Blockchain::BSC => user_data.wallets.bsc_wallet = new_wallet,
            Blockchain::SOL => user_data.wallets.sol_wallet = new_wallet,
            Blockchain::ETH => user_data.wallets.eth_wallet = new_wallet,
            Blockchain::BASE => user_data.wallets.base_wallet = new_wallet,
        }

        user_data.set_wallets_generated(true);

        user_data.update_last_seen();

        crate::service::DbService::save_user_wallets(&user_data.wallets).await?;

        self.cache_user_data(user_data.clone()).await;

        Ok(user_data)
    }
}
