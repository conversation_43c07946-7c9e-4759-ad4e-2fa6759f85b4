use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use crate::model::blockchain::Blockchain;
use crate::model::wallet_config::WalletConfig;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserConfig {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    #[serde(default = "default_preset_settings")]
    pub preset_setting: Vec<f64>,
    #[serde(default)]
    pub nonce: i32,
    #[serde(default)]
    pub retired: bool,
    pub referrer_code: Option<String>,
    pub referrer_wallet: Option<String>,
    pub referral_code: Option<String>,
    pub referral_date: Option<String>,
    #[serde(default = "default_schedule")]
    pub schedule: String,
    #[serde(default)]
    pub burn_fee: bool,
    #[serde(default)]
    pub auto_buy: bool,
    #[serde(default = "default_zero_string")]
    pub auto_buy_amount: String,
    #[serde(default = "default_zero_string")]
    pub auto_sell_amount: String,
    #[serde(default)]
    pub current_blockchain: Blockchain,
    pub sol_config: Option<WalletConfig>,
    pub bsc_config: Option<WalletConfig>,
    pub base_config: Option<WalletConfig>,
    pub eth_config: Option<WalletConfig>,
    pub bsc_current_contract_address: Option<String>,
    pub sol_current_contract_address: Option<String>,
    pub base_current_contract_address: Option<String>,
    pub eth_current_contract_address: Option<String>,
    pub bsc_current_token_name: Option<String>,
    pub sol_current_token_name: Option<String>,
    pub base_current_token_name: Option<String>,
    pub eth_current_token_name: Option<String>,
}

fn default_preset_settings() -> Vec<f64> {
    vec![0.01, 1.0, 5.0, 10.0]
}

fn default_schedule() -> String {
    "60".to_string()
}

fn default_zero_string() -> String {
    "0".to_string()
}

impl UserConfig {
    pub fn new(user_id: ObjectId) -> Self {
        let uuid = uuid::Uuid::new_v4().to_string();

        Self {
            id: None,
            user_id,
            preset_setting: default_preset_settings(),
            nonce: 0,
            retired: false,
            referrer_code: None,
            referrer_wallet: None,
            referral_code: Some(uuid),
            referral_date: None,
            schedule: default_schedule(),
            burn_fee: false,
            auto_buy: false,
            auto_buy_amount: default_zero_string(),
            auto_sell_amount: default_zero_string(),
            current_blockchain: Blockchain::BSC,
            sol_config: Some(WalletConfig::sol_default()),
            bsc_config: Some(WalletConfig::bsc_default()),
            base_config: Some(WalletConfig::base_default()),
            eth_config: Some(WalletConfig::eth_default()),
            bsc_current_contract_address: None,
            sol_current_contract_address: None,
            base_current_contract_address: None,
            eth_current_contract_address: None,
            bsc_current_token_name: None,
            sol_current_token_name: None,
            base_current_token_name: None,
            eth_current_token_name: None,
        }
    }

    pub fn get_current_contract_address(&self) -> Option<&String> {
        match self.current_blockchain {
            Blockchain::BSC => self.bsc_current_contract_address.as_ref(),
            Blockchain::SOL => self.sol_current_contract_address.as_ref(),
            Blockchain::ETH => self.eth_current_contract_address.as_ref(),
            Blockchain::BASE => self.base_current_contract_address.as_ref(),
        }
    }

    pub fn get_current_token_name(&self) -> Option<&String> {
        match self.current_blockchain {
            Blockchain::BSC => self.bsc_current_token_name.as_ref(),
            Blockchain::SOL => self.sol_current_token_name.as_ref(),
            Blockchain::ETH => self.eth_current_token_name.as_ref(),
            Blockchain::BASE => self.base_current_token_name.as_ref(),
        }
    }

    pub fn get_config(&self, blockchain: &Blockchain) -> Option<&WalletConfig> {
        match blockchain {
            Blockchain::BSC => self.bsc_config.as_ref(),
            Blockchain::SOL => self.sol_config.as_ref(),
            Blockchain::ETH => self.eth_config.as_ref(),
            Blockchain::BASE => self.base_config.as_ref(),
        }
    }

    pub fn get_blockchain_config(&self, blockchain: &Blockchain) -> WalletConfig {
        match blockchain {
            Blockchain::BSC => self.bsc_config.as_ref().cloned().unwrap_or_else(|| WalletConfig::bsc_default()),
            Blockchain::SOL => self.sol_config.as_ref().cloned().unwrap_or_else(|| WalletConfig::sol_default()),
            Blockchain::ETH => self.eth_config.as_ref().cloned().unwrap_or_else(|| WalletConfig::eth_default()),
            Blockchain::BASE => self.base_config.as_ref().cloned().unwrap_or_else(|| WalletConfig::base_default()),
        }
    }

    pub fn reset_blockchain_config(&mut self, blockchain: &Blockchain) {
        match blockchain {
            Blockchain::BSC => self.bsc_config = Some(WalletConfig::bsc_default()),
            Blockchain::SOL => self.sol_config = Some(WalletConfig::sol_default()),
            Blockchain::ETH => self.eth_config = Some(WalletConfig::eth_default()),
            Blockchain::BASE => self.base_config = Some(WalletConfig::base_default()),
        }
    }
}
